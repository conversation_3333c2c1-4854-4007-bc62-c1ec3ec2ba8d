const express = require('express');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');

const router = express.Router();

/**
 * @route POST /api/auth/register
 * @desc Inscription d'un nouvel utilisateur
 */
router.post('/register', [
  body('nom_utilisateur')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Le nom doit contenir entre 2 et 100 caractères'),
  
  body('prenom_utilisateur')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Le prénom doit contenir entre 2 et 100 caractères'),
  
  body('email_utilisateur')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email invalide'),
  
  body('mot_de_passe')
    .isLength({ min: 6 })
    .withMessage('Le mot de passe doit contenir au moins 6 caractères'),
  
  body('contact_utilisateur')
    .optional()
    .isMobilePhone('fr-FR')
    .withMessage('Numéro de téléphone invalide')
], async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const {
      nom_utilisateur,
      prenom_utilisateur,
      email_utilisateur,
      mot_de_passe,
      contact_utilisateur,
      adresse_utilisateur
    } = req.body;

    // Vérifier si l'utilisateur existe déjà
    const existingUser = await User.findByEmail(email_utilisateur);
    if (existingUser) {
      return res.status(409).json({
        error: 'Un utilisateur avec cet email existe déjà'
      });
    }

    // Créer le nouvel utilisateur
    const newUser = await User.create({
      nom_utilisateur,
      prenom_utilisateur,
      email_utilisateur,
      mot_de_passe,
      contact_utilisateur,
      adresse_utilisateur
    });

    // Générer le token JWT
    const token = jwt.sign(
      { 
        userId: newUser.id,
        email: newUser.email_utilisateur 
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    res.status(201).json({
      message: 'Utilisateur créé avec succès',
      user: newUser.toJSON(),
      token
    });

  } catch (error) {
    console.error('Erreur inscription:', error);
    res.status(500).json({
      error: 'Erreur lors de l\'inscription',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route POST /api/auth/login
 * @desc Connexion d'un utilisateur
 */
router.post('/login', [
  body('email_utilisateur')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email invalide'),
  
  body('mot_de_passe')
    .notEmpty()
    .withMessage('Mot de passe requis')
], async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const { email_utilisateur, mot_de_passe } = req.body;

    // Trouver l'utilisateur
    const user = await User.findByEmail(email_utilisateur);
    if (!user) {
      return res.status(401).json({
        error: 'Email ou mot de passe incorrect'
      });
    }

    // Vérifier si le compte est actif
    if (!user.isActive) {
      return res.status(403).json({
        error: 'Compte désactivé. Contactez l\'administrateur.'
      });
    }

    // Vérifier le mot de passe
    const isPasswordValid = await user.verifyPassword(mot_de_passe);
    if (!isPasswordValid) {
      return res.status(401).json({
        error: 'Email ou mot de passe incorrect'
      });
    }

    // Générer le token JWT
    const token = jwt.sign(
      { 
        userId: user.id,
        email: user.email_utilisateur 
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    res.json({
      message: 'Connexion réussie',
      user: user.toJSON(),
      token
    });

  } catch (error) {
    console.error('Erreur connexion:', error);
    res.status(500).json({
      error: 'Erreur lors de la connexion',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route POST /api/auth/verify-token
 * @desc Vérification d'un token JWT
 */
router.post('/verify-token', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        error: 'Token requis'
      });
    }

    // Vérifier le token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Récupérer l'utilisateur
    const user = await User.findById(decoded.userId);
    if (!user || !user.isActive) {
      return res.status(401).json({
        error: 'Token invalide ou utilisateur inactif'
      });
    }

    res.json({
      valid: true,
      user: user.toJSON()
    });

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Token invalide'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Token expiré'
      });
    }

    console.error('Erreur vérification token:', error);
    res.status(500).json({
      error: 'Erreur lors de la vérification du token'
    });
  }
});

/**
 * @route POST /api/auth/refresh-token
 * @desc Renouvellement d'un token JWT
 */
router.post('/refresh-token', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        error: 'Token requis'
      });
    }

    // Vérifier le token (même s'il est expiré)
    const decoded = jwt.verify(token, process.env.JWT_SECRET, { ignoreExpiration: true });
    
    // Récupérer l'utilisateur
    const user = await User.findById(decoded.userId);
    if (!user || !user.isActive) {
      return res.status(401).json({
        error: 'Utilisateur invalide ou inactif'
      });
    }

    // Générer un nouveau token
    const newToken = jwt.sign(
      { 
        userId: user.id,
        email: user.email_utilisateur 
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    res.json({
      message: 'Token renouvelé avec succès',
      token: newToken,
      user: user.toJSON()
    });

  } catch (error) {
    console.error('Erreur renouvellement token:', error);
    res.status(500).json({
      error: 'Erreur lors du renouvellement du token'
    });
  }
});

module.exports = router;
