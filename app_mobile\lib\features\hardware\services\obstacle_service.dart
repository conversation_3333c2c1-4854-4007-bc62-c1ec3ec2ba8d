import 'dart:async';
import 'dart:math';
import 'package:canne_connectee/features/communication/services/mqtt_service.dart';
import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart';

class ObstacleDetection {
  final String obstacleType;
  final double distance;
  final double latitude;
  final double longitude;
  final String description;
  final DateTime timestamp;
  final String severity; // 'low', 'medium', 'high'

  ObstacleDetection({
    required this.obstacleType,
    required this.distance,
    required this.latitude,
    required this.longitude,
    required this.description,
    required this.timestamp,
    required this.severity,
  });
}

class ObstacleService {
  static final ObstacleService _instance = ObstacleService._internal();
  factory ObstacleService() => _instance;
  ObstacleService._internal();

  final MqttService _mqttService = MqttService();
  
  CameraController? _cameraController;
  bool _isDetecting = false;
  bool _isInitialized = false;
  Timer? _detectionTimer;
  
  // Simulation d'obstacles pour la démo
  final List<String> _obstacleTypes = [
    'vehicle',
    'pedestrian',
    'bicycle',
    'construction',
    'pothole',
    'barrier',
    'tree_branch',
    'stairs',
    'curb',
    'pole'
  ];

  bool get isDetecting => _isDetecting;
  bool get isInitialized => _isInitialized;

  Future<void> initialize() async {
    try {
      // Obtenir la liste des caméras disponibles
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        if (kDebugMode) {
          print('ObstacleService: Aucune caméra disponible');
        }
        return;
      }

      // Utiliser la caméra arrière par défaut
      final camera = cameras.first;
      
      _cameraController = CameraController(
        camera,
        ResolutionPreset.medium,
        enableAudio: false,
      );

      await _cameraController!.initialize();
      _isInitialized = true;

      if (kDebugMode) {
        print('ObstacleService: Initialisé avec succès');
      }
    } catch (e) {
      if (kDebugMode) {
        print('ObstacleService: Erreur d\'initialisation: $e');
      }
      _isInitialized = false;
    }
  }

  Future<void> startDetection() async {
    if (!_isInitialized || _isDetecting) return;

    _isDetecting = true;
    
    // Démarrer la détection périodique (simulation)
    _detectionTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _simulateObstacleDetection();
    });

    if (kDebugMode) {
      print('ObstacleService: Détection d\'obstacles démarrée');
    }
  }

  void stopDetection() {
    _isDetecting = false;
    _detectionTimer?.cancel();
    _detectionTimer = null;

    if (kDebugMode) {
      print('ObstacleService: Détection d\'obstacles arrêtée');
    }
  }

  void _simulateObstacleDetection() {
    final random = Random();
    
    // 30% de chance de détecter un obstacle
    if (random.nextDouble() < 0.3) {
      final obstacleType = _obstacleTypes[random.nextInt(_obstacleTypes.length)];
      final distance = 1.0 + random.nextDouble() * 9.0; // 1-10 mètres
      final severity = _calculateSeverity(obstacleType, distance);
      
      final detection = ObstacleDetection(
        obstacleType: obstacleType,
        distance: distance,
        latitude: 0.0, // À remplacer par la vraie position GPS
        longitude: 0.0,
        description: _getObstacleDescription(obstacleType, distance),
        timestamp: DateTime.now(),
        severity: severity,
      );

      _publishObstacleDetection(detection);
    }
  }

  String _calculateSeverity(String obstacleType, double distance) {
    // Calculer la gravité basée sur le type et la distance
    if (distance < 2.0) {
      return 'high';
    } else if (distance < 5.0) {
      if (['vehicle', 'construction', 'stairs'].contains(obstacleType)) {
        return 'high';
      }
      return 'medium';
    } else {
      return 'low';
    }
  }

  String _getObstacleDescription(String obstacleType, double distance) {
    final distanceStr = '${distance.toStringAsFixed(1)}m';
    
    switch (obstacleType) {
      case 'vehicle':
        return 'Véhicule détecté à $distanceStr';
      case 'pedestrian':
        return 'Piéton détecté à $distanceStr';
      case 'bicycle':
        return 'Vélo détecté à $distanceStr';
      case 'construction':
        return 'Zone de travaux à $distanceStr';
      case 'pothole':
        return 'Nid-de-poule détecté à $distanceStr';
      case 'barrier':
        return 'Barrière détectée à $distanceStr';
      case 'tree_branch':
        return 'Branche d\'arbre à $distanceStr';
      case 'stairs':
        return 'Escaliers détectés à $distanceStr';
      case 'curb':
        return 'Bordure de trottoir à $distanceStr';
      case 'pole':
        return 'Poteau détecté à $distanceStr';
      default:
        return 'Obstacle détecté à $distanceStr';
    }
  }

  void _publishObstacleDetection(ObstacleDetection detection) {
    // Publier via MQTT
    _mqttService.publishObstacleDetection(
      obstacleType: detection.obstacleType,
      distance: detection.distance,
      latitude: detection.latitude,
      longitude: detection.longitude,
      description: detection.description,
    );

    // Publier aussi comme alerte si c'est grave
    if (detection.severity == 'high') {
      _mqttService.publishAlert(
        alertType: 'OBSTACLE_WARNING',
        message: 'Attention: ${detection.description}',
        priority: 'HIGH',
        latitude: detection.latitude,
        longitude: detection.longitude,
        additionalData: {
          'obstacle_type': detection.obstacleType,
          'distance': detection.distance,
          'severity': detection.severity,
        },
      );
    }

    if (kDebugMode) {
      print('🚧 Obstacle détecté: ${detection.description} (${detection.severity})');
    }
  }

  // Méthode pour la détection manuelle d'obstacle
  void reportObstacle({
    required String obstacleType,
    required double latitude,
    required double longitude,
    String? customDescription,
  }) {
    final description = customDescription ?? _getObstacleDescription(obstacleType, 0.0);
    
    final detection = ObstacleDetection(
      obstacleType: obstacleType,
      distance: 0.0, // Distance inconnue pour un rapport manuel
      latitude: latitude,
      longitude: longitude,
      description: description,
      timestamp: DateTime.now(),
      severity: 'medium',
    );

    _publishObstacleDetection(detection);

    if (kDebugMode) {
      print('📝 Obstacle signalé manuellement: $description');
    }
  }

  // Obtenir les types d'obstacles disponibles
  List<String> getAvailableObstacleTypes() {
    return List.from(_obstacleTypes);
  }

  // Obtenir les descriptions localisées des types d'obstacles
  Map<String, String> getObstacleTypeDescriptions() {
    return {
      'vehicle': 'Véhicule',
      'pedestrian': 'Piéton',
      'bicycle': 'Vélo',
      'construction': 'Travaux',
      'pothole': 'Nid-de-poule',
      'barrier': 'Barrière',
      'tree_branch': 'Branche d\'arbre',
      'stairs': 'Escaliers',
      'curb': 'Bordure',
      'pole': 'Poteau',
    };
  }

  // Prendre une photo pour l'analyse d'obstacles
  Future<String?> captureObstaclePhoto() async {
    if (!_isInitialized || _cameraController == null) {
      return null;
    }

    try {
      final image = await _cameraController!.takePicture();
      return image.path;
    } catch (e) {
      if (kDebugMode) {
        print('Erreur capture photo: $e');
      }
      return null;
    }
  }

  void dispose() {
    stopDetection();
    _cameraController?.dispose();
    _cameraController = null;
    _isInitialized = false;
  }
}
