const mysql = require('mysql2/promise');
require('dotenv').config();

// Configuration de la base de données
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'blind_cane_db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  charset: 'utf8mb4'
};

// Pool de connexions
let pool;

/**
 * Initialise le pool de connexions MySQL
 */
const initializeDatabase = async () => {
  try {
    console.log('🔌 Initialisation de la connexion MySQL...');
    
    pool = mysql.createPool(dbConfig);
    
    // Test de connexion
    const connection = await pool.getConnection();
    console.log('✅ Connexion MySQL établie avec succès');
    console.log(`📊 Base de données: ${dbConfig.database}`);
    console.log(`🏠 Serveur: ${dbConfig.host}:${dbConfig.port}`);
    
    connection.release();
    
    return pool;
  } catch (error) {
    console.error('❌ Erreur de connexion MySQL:', error.message);
    throw error;
  }
};

/**
 * Obtient une connexion du pool
 */
const getConnection = async () => {
  if (!pool) {
    throw new Error('Base de données non initialisée. Appelez initializeDatabase() d\'abord.');
  }
  return await pool.getConnection();
};

/**
 * Exécute une requête SQL
 */
const executeQuery = async (query, params = []) => {
  const connection = await getConnection();
  try {
    const [results] = await connection.execute(query, params);
    return results;
  } catch (error) {
    console.error('❌ Erreur requête SQL:', error.message);
    console.error('📝 Requête:', query);
    console.error('📋 Paramètres:', params);
    throw error;
  } finally {
    connection.release();
  }
};

/**
 * Exécute une transaction
 */
const executeTransaction = async (queries) => {
  const connection = await getConnection();
  try {
    await connection.beginTransaction();
    
    const results = [];
    for (const { query, params } of queries) {
      const [result] = await connection.execute(query, params || []);
      results.push(result);
    }
    
    await connection.commit();
    return results;
  } catch (error) {
    await connection.rollback();
    console.error('❌ Erreur transaction:', error.message);
    throw error;
  } finally {
    connection.release();
  }
};

/**
 * Ferme le pool de connexions
 */
const closeDatabase = async () => {
  if (pool) {
    await pool.end();
    console.log('🔌 Connexions MySQL fermées');
  }
};

// Gestion propre de la fermeture
process.on('SIGINT', async () => {
  console.log('\n🛑 Arrêt du serveur...');
  await closeDatabase();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Arrêt du serveur...');
  await closeDatabase();
  process.exit(0);
});

module.exports = {
  initializeDatabase,
  getConnection,
  executeQuery,
  executeTransaction,
  closeDatabase,
  pool: () => pool
};
