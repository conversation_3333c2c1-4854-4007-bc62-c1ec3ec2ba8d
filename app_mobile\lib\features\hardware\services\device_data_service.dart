import 'dart:convert';
import 'package:canne_connectee/shared/models/call_record.dart';
import 'package:canne_connectee/shared/models/contact.dart';
import 'package:canne_connectee/shared/models/message.dart';
import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';

class DeviceDataService {
  static final DeviceDataService _instance = DeviceDataService._internal();
  factory DeviceDataService() => _instance;
  DeviceDataService._internal();

  bool _isInitialized = false;

  /// Initialise le service et demande les permissions
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Demander toutes les permissions nécessaires
      final permissions = await _requestPermissions();
      
      if (permissions) {
        _isInitialized = true;
        
        if (kDebugMode) {
          print('DeviceDataService: Initialisé avec succès');
        }
        
        return true;
      } else {
        if (kDebugMode) {
          print('DeviceDataService: Permissions refusées');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('DeviceDataService: Erreur initialisation: $e');
      }
      return false;
    }
  }

  /// Demande toutes les permissions nécessaires
  Future<bool> _requestPermissions() async {
    try {
      // Permissions temporairement désactivées
      final permissions = <Permission>[];

      Map<Permission, PermissionStatus> statuses = await permissions.request();
      
      bool allGranted = true;
      for (final permission in permissions) {
        final status = statuses[permission];
        if (status != PermissionStatus.granted) {
          allGranted = false;
          if (kDebugMode) {
            print('DeviceDataService: Permission ${permission.toString()} refusée: $status');
          }
        }
      }

      return allGranted;
    } catch (e) {
      if (kDebugMode) {
        print('DeviceDataService: Erreur demande permissions: $e');
      }
      return false;
    }
  }

  /// Récupère l'historique des appels de l'appareil (fonctionnalité désactivée temporairement)
  Future<List<CallRecord>> getDeviceCallHistory({int limit = 50}) async {
    if (kDebugMode) {
      print('DeviceDataService: Récupération appels désactivée temporairement');
    }
    return [];
  }

  /// Récupère les SMS de l'appareil (fonctionnalité désactivée temporairement)
  Future<List<Message>> getDeviceSMS({int limit = 100}) async {
    if (kDebugMode) {
      print('DeviceDataService: Récupération SMS désactivée temporairement');
    }
    return [];
  }

  /// Récupère les contacts de l'appareil (fonctionnalité désactivée temporairement)
  Future<List<Contact>> getDeviceContacts({int limit = 200}) async {
    if (kDebugMode) {
      print('DeviceDataService: Récupération contacts désactivée temporairement');
    }
    return [];
  }

  /// Envoie un SMS via l'API native (fonctionnalité désactivée temporairement)
  Future<bool> sendDeviceSMS(String phoneNumber, String message) async {
    if (kDebugMode) {
      print('DeviceDataService: Envoi SMS désactivé temporairement');
    }
    return false;
  }



  /// Détermine si un numéro est un numéro d'urgence
  bool _isEmergencyNumber(String phoneNumber) {
    final emergencyNumbers = ['17', '15', '18', '112', '911'];
    return emergencyNumbers.contains(phoneNumber.replaceAll(RegExp(r'[^\d]'), ''));
  }

  /// Détermine le type de contact basé sur le nom et le numéro
  ContactType _determineContactType(String name, String phone) {
    final lowerName = name.toLowerCase();
    
    if (_isEmergencyNumber(phone)) {
      return ContactType.emergency;
    } else if (lowerName.contains('docteur') || lowerName.contains('dr') || 
               lowerName.contains('médecin') || lowerName.contains('hopital')) {
      return ContactType.medical;
    } else if (lowerName.contains('pharmacie')) {
      return ContactType.pharmacy;
    } else if (lowerName.contains('taxi') || lowerName.contains('transport')) {
      return ContactType.transport;
    } else if (lowerName.contains('famille') || lowerName.contains('papa') || 
               lowerName.contains('maman') || lowerName.contains('fils') || 
               lowerName.contains('fille')) {
      return ContactType.family;
    } else {
      return ContactType.personal;
    }
  }

  /// Génère un ID de contact basé sur le numéro de téléphone
  String _getContactIdFromPhone(String phoneNumber) {
    return 'phone_${phoneNumber.replaceAll(RegExp(r'[^\d]'), '')}';
  }

  /// Vérifie si un message contient des mots-clés d'urgence
  bool _containsEmergencyKeywords(String message) {
    final emergencyKeywords = [
      'urgence', 'aide', 'secours', 'danger', 'accident', 'malade', 
      'douleur', 'chute', 'problème', 'sos', 'emergency', 'help'
    ];
    
    final lowerMessage = message.toLowerCase();
    return emergencyKeywords.any((keyword) => lowerMessage.contains(keyword));
  }

  /// Vérifie le statut des permissions
  Future<Map<String, bool>> getPermissionsStatus() async {
    return {
      'phone': false,    // Désactivé temporairement
      'sms': false,      // Désactivé temporairement
      'contacts': false, // Désactivé temporairement
    };
  }

  /// Nettoie les ressources
  Future<void> dispose() async {
    _isInitialized = false;
    
    if (kDebugMode) {
      print('DeviceDataService: Ressources libérées');
    }
  }
}
