{"name": "blind-cane-api", "version": "1.0.0", "description": "API REST pour l'application Canne Connectée avec base de données MySQL", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-database.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.0", "dotenv": "^16.3.1", "express-validator": "^7.0.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["api", "rest", "mysql", "authentication", "blind-cane", "accessibility", "sos", "emergency"], "author": "Blind Cane Team", "license": "MIT", "engines": {"node": ">=16.0.0"}}