import 'package:canne_connectee/shared/services/integrated_data_service.dart';
import 'package:flutter/material.dart';

class PermissionsStatusWidget extends StatefulWidget {
  const PermissionsStatusWidget({super.key});

  @override
  State<PermissionsStatusWidget> createState() => _PermissionsStatusWidgetState();
}

class _PermissionsStatusWidgetState extends State<PermissionsStatusWidget> {
  final IntegratedDataService _integratedService = IntegratedDataService();
  Map<String, bool> _permissions = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPermissions();
  }

  Future<void> _loadPermissions() async {
    try {
      final permissions = await _integratedService.getPermissionsStatus();
      setState(() {
        _permissions = permissions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SizedBox(
        height: 20,
        width: 20,
        child: CircularProgressIndicator(strokeWidth: 2),
      );
    }

    final hasAnyPermission = _permissions.values.any((granted) => granted);
    
    return GestureDetector(
      onTap: () => _showPermissionsDialog(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: hasAnyPermission 
              ? Colors.green.withOpacity(0.1) 
              : Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: hasAnyPermission ? Colors.green : Colors.orange,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              hasAnyPermission ? Icons.check_circle : Icons.warning,
              color: hasAnyPermission ? Colors.green : Colors.orange,
              size: 16,
            ),
            const SizedBox(width: 6),
            Text(
              hasAnyPermission ? 'Connecté' : 'Limité',
              style: TextStyle(
                color: hasAnyPermission ? Colors.green : Colors.orange,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showPermissionsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.security, color: Colors.blue),
            SizedBox(width: 8),
            Text('Accès aux données'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'État des permissions pour accéder aux données de l\'appareil:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),
            _buildPermissionItem('Contacts', _permissions['contacts'] ?? false),
            _buildPermissionItem('Téléphone', _permissions['phone'] ?? false),
            _buildPermissionItem('SMS', _permissions['sms'] ?? false),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Fonctionnalités disponibles:',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 8),
                  if (_permissions['contacts'] == true)
                    const Text('✅ Accès aux contacts de l\'appareil'),
                  if (_permissions['phone'] == true)
                    const Text('✅ Historique des appels réels'),
                  if (_permissions['sms'] == true)
                    const Text('✅ SMS natifs'),
                  if (!_permissions.values.any((granted) => granted))
                    const Text('⚠️ Fonctionnalités limitées aux données locales'),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
          if (!_integratedService.isDeviceAccessEnabled)
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _requestPermissions();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF7900),
                foregroundColor: Colors.white,
              ),
              child: const Text('Activer'),
            ),
        ],
      ),
    );
  }

  Widget _buildPermissionItem(String name, bool granted) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            granted ? Icons.check_circle : Icons.cancel,
            color: granted ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(name),
        ],
      ),
    );
  }

  Future<void> _requestPermissions() async {
    final success = await _integratedService.requestPermissions();
    
    if (success) {
      await _loadPermissions();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Permissions accordées ! Accès aux données activé.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Permissions refusées. Fonctionnalités limitées.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
