import 'dart:developer' as developer;
import 'package:logging/logging.dart';
import '../config/app_config.dart';

/// Service de logging centralisé pour l'application
class AppLogger {
  static final Map<String, Logger> _loggers = {};
  static bool _initialized = false;
  
  /// Initialise le système de logging
  static void initialize() {
    if (_initialized) return;
    
    // Configuration du niveau de log selon l'environnement
    Logger.root.level = EnvironmentConfig.enableDebugLogs 
        ? Level.ALL 
        : Level.INFO;
    
    // Configuration du handler de logging
    Logger.root.onRecord.listen((record) {
      final message = _formatLogMessage(record);
      
      // En développement : affichage dans la console
      if (EnvironmentConfig.isDevelopment) {
        developer.log(
          message,
          name: record.loggerName,
          level: record.level.value,
          error: record.error,
          stackTrace: record.stackTrace,
        );
      }
      
      // En production : envoi vers service de logging externe
      if (EnvironmentConfig.isProduction) {
        _sendToExternalLogging(record);
      }
    });
    
    _initialized = true;
  }
  
  /// Obtient un logger pour une classe spécifique
  static Logger getLogger(String name) {
    return _loggers.putIfAbsent(name, () => Logger(name));
  }
  
  /// Formate un message de log
  static String _formatLogMessage(LogRecord record) {
    final timestamp = record.time.toIso8601String();
    final level = record.level.name.padRight(7);
    final logger = record.loggerName.padRight(20);
    final message = record.message;
    
    var formatted = '[$timestamp] $level [$logger] $message';
    
    if (record.error != null) {
      formatted += '\nError: ${record.error}';
    }
    
    if (record.stackTrace != null && EnvironmentConfig.enableDebugLogs) {
      formatted += '\nStackTrace:\n${record.stackTrace}';
    }
    
    return formatted;
  }
  
  /// Envoie les logs vers un service externe (Firebase, Sentry, etc.)
  static void _sendToExternalLogging(LogRecord record) {
    // TODO: Implémenter l'envoi vers Firebase Crashlytics ou autre
    // FirebaseCrashlytics.instance.log(record.message);
    
    if (record.level >= Level.SEVERE && record.error != null) {
      // FirebaseCrashlytics.instance.recordError(
      //   record.error,
      //   record.stackTrace,
      //   fatal: record.level == Level.SHOUT,
      // );
    }
  }
}

/// Extension pour faciliter l'utilisation du logging
extension LoggerExtension on Object {
  Logger get logger => AppLogger.getLogger(runtimeType.toString());
}

/// Niveaux de log personnalisés pour l'application
class AppLogLevel {
  static const Level voice = Level('VOICE', 850);
  static const Level tts = Level('TTS', 850);
  static const Level api = Level('API', 850);
  static const Level bluetooth = Level('BLUETOOTH', 850);
  static const Level location = Level('LOCATION', 850);
}

/// Utilitaires de logging spécialisés
class LogUtils {
  static final Logger _logger = AppLogger.getLogger('LogUtils');
  
  /// Log une commande vocale
  static void logVoiceCommand(String command, bool success, {String? error}) {
    if (success) {
      _logger.log(AppLogLevel.voice, 'Commande vocale exécutée: $command');
    } else {
      _logger.log(AppLogLevel.voice, 'Échec commande vocale: $command', error);
    }
  }
  
  /// Log une synthèse vocale
  static void logTTS(String text, bool success, {String? error}) {
    if (success) {
      _logger.log(AppLogLevel.tts, 'TTS réussi: "${text.substring(0, text.length.clamp(0, 50))}..."');
    } else {
      _logger.log(AppLogLevel.tts, 'Échec TTS: $text', error);
    }
  }
  
  /// Log un appel API
  static void logApiCall(String endpoint, int statusCode, {String? error}) {
    if (statusCode >= 200 && statusCode < 300) {
      _logger.log(AppLogLevel.api, 'API SUCCESS: $endpoint ($statusCode)');
    } else {
      _logger.log(AppLogLevel.api, 'API ERROR: $endpoint ($statusCode)', error);
    }
  }
  
  /// Log une connexion Bluetooth
  static void logBluetoothConnection(String device, bool success, {String? error}) {
    if (success) {
      _logger.log(AppLogLevel.bluetooth, 'Connexion Bluetooth réussie: $device');
    } else {
      _logger.log(AppLogLevel.bluetooth, 'Échec connexion Bluetooth: $device', error);
    }
  }
  
  /// Log une mise à jour de localisation
  static void logLocationUpdate(double lat, double lng, double accuracy) {
    _logger.log(AppLogLevel.location, 
        'Position mise à jour: ($lat, $lng) précision: ${accuracy}m');
  }
  
  /// Log une alerte SOS
  static void logSOSAlert(String userId, String location) {
    _logger.severe('🚨 ALERTE SOS déclenchée - User: $userId, Location: $location');
  }
}
