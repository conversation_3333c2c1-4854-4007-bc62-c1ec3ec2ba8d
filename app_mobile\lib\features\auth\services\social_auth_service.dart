import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:crypto/crypto.dart';
import 'package:http/http.dart' as http;
import '../../../shared/models/user.dart';
import 'auth_service.dart';

/// Types de fournisseurs d'authentification sociale
enum SocialProvider {
  google('Google', 'assets/icons/google.png'),
  apple('Apple', 'assets/icons/apple.png'),
  facebook('Facebook', 'assets/icons/facebook.png'),
  microsoft('Microsoft', 'assets/icons/microsoft.png');

  const SocialProvider(this.displayName, this.iconPath);
  final String displayName;
  final String iconPath;
}

/// Modèle pour les données d'authentification sociale
class SocialAuthData {
  final String providerId;
  final String providerName;
  final String email;
  final String? displayName;
  final String? photoUrl;
  final String? accessToken;
  final String? idToken;

  SocialAuthData({
    required this.providerId,
    required this.providerName,
    required this.email,
    this.displayName,
    this.photoUrl,
    this.accessToken,
    this.idToken,
  });

  Map<String, dynamic> toJson() => {
    'provider_id': providerId,
    'provider_name': providerName,
    'email': email,
    'display_name': displayName,
    'photo_url': photoUrl,
    'access_token': accessToken,
    'id_token': idToken,
  };
}

/// Service d'authentification sociale
class SocialAuthService {
  static final SocialAuthService _instance = SocialAuthService._internal();
  factory SocialAuthService() => _instance;
  SocialAuthService._internal();

  static const String _baseUrl = 'http://192.168.252.246:3000/api';
  final AuthService _authService = AuthService();

  // Configuration Google Sign-In
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      'email',
      'profile',
    ],
  );

  /// Connexion avec Google
  Future<Map<String, dynamic>> signInWithGoogle({required UserType userType}) async {
    try {
      if (kDebugMode) {
        print('SocialAuth: Début connexion Google');
      }

      // Déconnexion préalable pour forcer la sélection de compte
      await _googleSignIn.signOut();

      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        return {
          'success': false,
          'message': 'Connexion Google annulée',
        };
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      final socialData = SocialAuthData(
        providerId: googleUser.id,
        providerName: 'google',
        email: googleUser.email,
        displayName: googleUser.displayName,
        photoUrl: googleUser.photoUrl,
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      return await _authenticateWithBackend(socialData, userType);

    } catch (e) {
      if (kDebugMode) {
        print('SocialAuth: Erreur Google Sign-In: $e');
      }
      return {
        'success': false,
        'message': 'Erreur lors de la connexion Google: $e',
      };
    }
  }

  /// Connexion avec Apple (iOS uniquement)
  Future<Map<String, dynamic>> signInWithApple({required UserType userType}) async {
    try {
      if (!Platform.isIOS) {
        return {
          'success': false,
          'message': 'Sign in with Apple n\'est disponible que sur iOS',
        };
      }

      if (kDebugMode) {
        print('SocialAuth: Début connexion Apple');
      }

      // Générer un nonce pour la sécurité
      final rawNonce = _generateNonce();
      final nonce = _sha256ofString(rawNonce);

      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: nonce,
      );

      final socialData = SocialAuthData(
        providerId: credential.userIdentifier,
        providerName: 'apple',
        email: credential.email ?? '',
        displayName: credential.givenName != null && credential.familyName != null
            ? '${credential.givenName} ${credential.familyName}'
            : null,
        idToken: credential.identityToken,
      );

      return await _authenticateWithBackend(socialData, userType);

    } catch (e) {
      if (kDebugMode) {
        print('SocialAuth: Erreur Apple Sign-In: $e');
      }
      return {
        'success': false,
        'message': 'Erreur lors de la connexion Apple: $e',
      };
    }
  }

  /// Authentification avec le backend
  Future<Map<String, dynamic>> _authenticateWithBackend(
    SocialAuthData socialData, 
    UserType userType
  ) async {
    try {
      if (kDebugMode) {
        print('SocialAuth: Authentification backend pour ${socialData.providerName}');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/auth/social-login'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'provider': socialData.providerName,
          'provider_id': socialData.providerId,
          'email': socialData.email,
          'display_name': socialData.displayName,
          'photo_url': socialData.photoUrl,
          'user_type': userType.name,
          'access_token': socialData.accessToken,
          'id_token': socialData.idToken,
        }),
      ).timeout(const Duration(seconds: 30));

      final data = json.decode(response.body);

      if (response.statusCode == 200 && data['success']) {
        // Créer l'utilisateur à partir de la réponse
        final user = User.fromJson(data['user']);
        
        // Sauvegarder dans AuthService
        await _authService.saveUserData(user, data['token']);

        return {
          'success': true,
          'user': user,
          'token': data['token'],
          'message': 'Connexion réussie avec ${socialData.providerName}',
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Erreur d\'authentification',
        };
      }

    } catch (e) {
      if (kDebugMode) {
        print('SocialAuth: Erreur backend: $e');
      }
      return {
        'success': false,
        'message': 'Erreur de connexion au serveur: $e',
      };
    }
  }

  /// Déconnexion de tous les fournisseurs sociaux
  Future<void> signOutFromAllProviders() async {
    try {
      // Déconnexion Google
      if (await _googleSignIn.isSignedIn()) {
        await _googleSignIn.signOut();
      }

      // Note: Apple ne nécessite pas de déconnexion explicite
      
      if (kDebugMode) {
        print('SocialAuth: Déconnexion de tous les fournisseurs');
      }
    } catch (e) {
      if (kDebugMode) {
        print('SocialAuth: Erreur déconnexion: $e');
      }
    }
  }

  /// Vérifie si un fournisseur est disponible sur la plateforme actuelle
  bool isProviderAvailable(SocialProvider provider) {
    switch (provider) {
      case SocialProvider.google:
        return true; // Disponible sur toutes les plateformes
      case SocialProvider.apple:
        return Platform.isIOS; // Uniquement sur iOS
      case SocialProvider.facebook:
        return false; // À implémenter
      case SocialProvider.microsoft:
        return false; // À implémenter
    }
  }

  /// Obtient la liste des fournisseurs disponibles
  List<SocialProvider> getAvailableProviders() {
    return SocialProvider.values
        .where((provider) => isProviderAvailable(provider))
        .toList();
  }

  /// Génère un nonce aléatoire pour Apple Sign-In
  String _generateNonce([int length = 32]) {
    const charset = '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = List.generate(length, (_) => charset[(DateTime.now().millisecondsSinceEpoch * 1000) % charset.length]);
    return random.join();
  }

  /// Génère un hash SHA256 d'une chaîne
  String _sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
}
