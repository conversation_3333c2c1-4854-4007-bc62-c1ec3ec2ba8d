import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import '../../features/location/services/gps_services.dart';

/// Service de localisation qui encapsule GpsService et ajoute des fonctionnalités supplémentaires
class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  final GpsService _gpsService = GpsService();

  /// Obtient la position actuelle
  Future<Position?> getCurrentPosition() async {
    return await _gpsService.getCurrentPosition();
  }

  /// Démarre le suivi GPS
  Future<void> startTracking() async {
    await _gpsService.startTracking();
  }

  /// Arrête le suivi GPS
  Future<void> stopTracking() async {
    await _gpsService.stopTracking();
  }

  /// Stream des positions
  Stream<Position> get positionStream => _gpsService.positionStream;

  /// Vérifie et demande les permissions de localisation
  Future<bool> checkLocationPermissions() async {
    return await _gpsService.checkAndRequestPermissions();
  }

  /// Formate une position pour l'affichage
  String formatPosition(Position position) {
    return _gpsService.formatPosition(position);
  }

  /// Calcule la distance entre deux points LatLng
  double calculateDistance(LatLng point1, LatLng point2) {
    return _gpsService.calculateDistance(
      point1.latitude,
      point1.longitude,
      point2.latitude,
      point2.longitude,
    );
  }

  /// Calcule la distance entre deux points avec coordonnées
  double calculateDistanceFromCoordinates(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    return _gpsService.calculateDistance(lat1, lon1, lat2, lon2);
  }

  /// Obtient le nom du lieu à partir des coordonnées
  /// Pour l'instant, retourne une description basique
  /// TODO: Intégrer un service de géocodage inverse (Google Maps, OpenStreetMap, etc.)
  Future<String> getLocationName(double latitude, double longitude) async {
    try {
      // Simulation d'un service de géocodage inverse
      // En production, vous devriez utiliser un vrai service comme:
      // - Google Maps Geocoding API
      // - OpenStreetMap Nominatim
      // - Mapbox Geocoding API
      
      // Pour l'instant, on retourne une description basée sur les coordonnées
      String latDirection = latitude >= 0 ? 'N' : 'S';
      String lonDirection = longitude >= 0 ? 'E' : 'O';
      
      // Simulation de zones connues (Abidjan, Côte d'Ivoire)
      if (latitude >= 5.0 && latitude <= 5.5 && longitude >= -4.5 && longitude <= -3.5) {
        // Zone d'Abidjan approximative
        if (latitude >= 5.25 && longitude >= -4.1) {
          return 'Plateau, Abidjan';
        } else if (latitude >= 5.3 && longitude <= -4.0) {
          return 'Cocody, Abidjan';
        } else if (latitude <= 5.2 && longitude >= -4.2) {
          return 'Treichville, Abidjan';
        } else if (latitude <= 5.15 && longitude <= -4.3) {
          return 'Koumassi, Abidjan';
        } else {
          return 'Abidjan, Côte d\'Ivoire';
        }
      }
      
      // Autres zones de Côte d'Ivoire
      if (latitude >= 4.0 && latitude <= 11.0 && longitude >= -8.5 && longitude <= -2.5) {
        return 'Côte d\'Ivoire';
      }
      
      // Zone générale Afrique de l'Ouest
      if (latitude >= -5.0 && latitude <= 20.0 && longitude >= -20.0 && longitude <= 15.0) {
        return 'Afrique de l\'Ouest';
      }
      
      // Coordonnées génériques
      return '${latitude.toStringAsFixed(4)}°${latDirection}, ${longitude.toStringAsFixed(4)}°${lonDirection}';
      
    } catch (e) {
      print('Erreur getLocationName: $e');
      return 'Lieu inconnu';
    }
  }

  /// Vérifie si une position est dans un rayon donné
  bool isWithinRadius(
    double centerLatitude,
    double centerLongitude,
    double targetLatitude,
    double targetLongitude,
    double radiusInMeters,
  ) {
    return _gpsService.isWithinRadius(
      centerLatitude,
      centerLongitude,
      targetLatitude,
      targetLongitude,
      radiusInMeters,
    );
  }

  /// Calcule le bearing entre deux points
  double calculateBearing(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return _gpsService.calculateBearing(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// Obtient des informations détaillées sur la position
  Map<String, dynamic> getPositionInfo(Position position) {
    return _gpsService.getPositionInfo(position);
  }

  /// Formate la vitesse
  String formatSpeed(double speedMps) {
    return _gpsService.formatSpeed(speedMps);
  }

  /// Formate la direction
  String formatHeading(double heading) {
    return _gpsService.formatHeading(heading);
  }

  /// Getters pour accéder aux propriétés du GpsService
  Position? get currentPosition => _gpsService.currentPosition;
  bool get isTracking => _gpsService.isTracking;
  String get locationStatus => _gpsService.locationStatus;
}
