import '../logging/app_logger.dart';

/// Interface pour les services qui nécessitent un nettoyage
abstract class Disposable {
  void dispose();
}

/// Interface pour les services qui nécessitent une initialisation
abstract class Initializable {
  Future<void> initialize();
}

/// Gestionnaire centralisé des services de l'application
class ServiceManager {
  static final ServiceManager _instance = ServiceManager._internal();
  factory ServiceManager() => _instance;
  ServiceManager._internal();

  final logger = AppLogger.getLogger('ServiceManager');
  final Map<Type, dynamic> _services = {};
  final Map<Type, bool> _initialized = {};
  bool _isDisposed = false;

  /// Enregistre un service
  void register<T>(T service) {
    if (_isDisposed) {
      throw StateError('ServiceManager has been disposed');
    }
    
    _services[T] = service;
    _initialized[T] = false;
    logger.info('Service enregistré: ${T.toString()}');
  }

  /// Récupère un service
  T get<T>() {
    if (_isDisposed) {
      throw StateError('ServiceManager has been disposed');
    }
    
    final service = _services[T];
    if (service == null) {
      throw StateError('Service ${T.toString()} not registered');
    }
    return service as T;
  }

  /// Vérifie si un service est enregistré
  bool isRegistered<T>() => _services.containsKey(T);

  /// Initialise tous les services
  Future<void> initializeAll() async {
    logger.info('Initialisation de tous les services...');
    
    final initializableServices = _services.values
        .whereType<Initializable>()
        .toList();

    for (final service in initializableServices) {
      try {
        await service.initialize();
        _initialized[service.runtimeType] = true;
        logger.info('Service initialisé: ${service.runtimeType}');
      } catch (e) {
        logger.severe('Erreur initialisation ${service.runtimeType}: $e', e);
      }
    }
    
    logger.info('${initializableServices.length} services initialisés');
  }

  /// Initialise un service spécifique
  Future<void> initialize<T>() async {
    final service = _services[T];
    if (service == null) {
      throw StateError('Service ${T.toString()} not registered');
    }

    if (service is Initializable && !(_initialized[T] ?? false)) {
      try {
        await service.initialize();
        _initialized[T] = true;
        logger.info('Service initialisé: ${T.toString()}');
      } catch (e) {
        logger.severe('Erreur initialisation ${T.toString()}: $e', e);
        rethrow;
      }
    }
  }

  /// Vérifie si un service est initialisé
  bool isInitialized<T>() => _initialized[T] ?? false;

  /// Libère toutes les ressources
  void dispose() {
    if (_isDisposed) return;
    
    logger.info('Libération de tous les services...');
    
    final disposableServices = _services.values
        .whereType<Disposable>()
        .toList();

    for (final service in disposableServices) {
      try {
        service.dispose();
        logger.info('Service libéré: ${service.runtimeType}');
      } catch (e) {
        logger.severe('Erreur libération ${service.runtimeType}: $e', e);
      }
    }

    _services.clear();
    _initialized.clear();
    _isDisposed = true;
    
    logger.info('${disposableServices.length} services libérés');
  }

  /// Libère un service spécifique
  void disposeService<T>() {
    final service = _services[T];
    if (service != null && service is Disposable) {
      try {
        service.dispose();
        logger.info('Service libéré: ${T.toString()}');
      } catch (e) {
        logger.severe('Erreur libération ${T.toString()}: $e', e);
      }
    }
    
    _services.remove(T);
    _initialized.remove(T);
  }

  /// Redémarre un service
  Future<void> restart<T>() async {
    logger.info('Redémarrage du service: ${T.toString()}');
    
    // Libérer d'abord
    disposeService<T>();
    
    // Puis réinitialiser si possible
    final service = _services[T];
    if (service is Initializable) {
      await initialize<T>();
    }
  }

  /// Obtient des informations sur l'état des services
  Map<String, dynamic> getServicesInfo() {
    return {
      'totalServices': _services.length,
      'initializedServices': _initialized.values.where((v) => v).length,
      'services': _services.keys.map((type) => {
        'type': type.toString(),
        'initialized': _initialized[type] ?? false,
        'isDisposable': _services[type] is Disposable,
        'isInitializable': _services[type] is Initializable,
      }).toList(),
    };
  }
}

/// Extension pour faciliter l'accès aux services
extension ServiceManagerExtension on Object {
  T getService<T>() => ServiceManager().get<T>();
}
