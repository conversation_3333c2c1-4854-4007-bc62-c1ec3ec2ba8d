import 'package:flutter/material.dart';
import '../../../shared/models/user.dart';
import 'enhanced_login_page.dart';
import 'register_with_type_page.dart';

/// Page simple de sélection du type d'utilisateur
class SimpleUserTypeSelectionPage extends StatelessWidget {
  const SimpleUserTypeSelectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              const SizedBox(height: 40),
              
              // Logo et titre
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFFFF7900),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.accessibility_new,
                  size: 60,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: 30),
              
              const Text(
                'Canne Connectée',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              const Text(
                'Choisissez votre profil',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 50),
              
              // Cartes de sélection du type d'utilisateur
              Expanded(
                child: Column(
                  children: [
                    _buildUserTypeCard(
                      context: context,
                      userType: UserType.aveugle,
                      onTap: () => _navigateToRegister(context, UserType.aveugle),
                    ),
                    
                    const SizedBox(height: 20),
                    
                    _buildUserTypeCard(
                      context: context,
                      userType: UserType.proche,
                      onTap: () => _navigateToRegister(context, UserType.proche),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 30),
              
              // Lien vers la connexion
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'Déjà un compte ? ',
                    style: TextStyle(color: Colors.grey),
                  ),
                  TextButton(
                    onPressed: () => _navigateToLogin(context),
                    child: const Text(
                      'Se connecter',
                      style: TextStyle(
                        color: Color(0xFFFF7900),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserTypeCard({
    required BuildContext context,
    required UserType userType,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Row(
            children: [
              // Icône
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFFF7900).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  userType.icon,
                  style: const TextStyle(fontSize: 32),
                ),
              ),
              
              const SizedBox(width: 20),
              
              // Texte
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      userType.displayName,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      userType.description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Flèche
              const Icon(
                Icons.arrow_forward_ios,
                color: Color(0xFFFF7900),
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToRegister(BuildContext context, UserType userType) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RegisterWithTypePage(userType: userType),
      ),
    );
  }

  void _navigateToLogin(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const EnhancedLoginPage(),
      ),
    );
  }
}
