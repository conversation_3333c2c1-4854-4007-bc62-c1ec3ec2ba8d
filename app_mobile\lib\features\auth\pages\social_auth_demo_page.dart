import 'package:flutter/material.dart';
import '../../../shared/models/user.dart';
import '../services/social_auth_service.dart';
import '../widgets/social_auth_buttons.dart';

/// Page de démonstration de l'authentification sociale
class SocialAuthDemoPage extends StatefulWidget {
  const SocialAuthDemoPage({super.key});

  @override
  State<SocialAuthDemoPage> createState() => _SocialAuthDemoPageState();
}

class _SocialAuthDemoPageState extends State<SocialAuthDemoPage> {
  final SocialAuthService _socialAuthService = SocialAuthService();
  UserType _selectedUserType = UserType.aveugle;
  bool _isLoading = false;
  String? _lastResult;

  @override
  Widget build(BuildContext context) {
    const primaryColor = Color(0xFFFF7900);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Authentification Sociale - Demo'),
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Titre et description
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '🔐 Authentification Sociale',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Testez les différentes options de connexion avec les fournisseurs sociaux.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Sélection du type d'utilisateur
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Type d\'utilisateur',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: _buildUserTypeCard(UserType.aveugle),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildUserTypeCard(UserType.proche),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Fournisseurs disponibles
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Fournisseurs Disponibles',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ..._socialAuthService.getAvailableProviders().map(
                      (provider) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: Colors.green,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(provider.displayName),
                          ],
                        ),
                      ),
                    ),
                    if (_socialAuthService.getAvailableProviders().isEmpty)
                      const Row(
                        children: [
                          Icon(
                            Icons.warning,
                            color: Colors.orange,
                            size: 20,
                          ),
                          SizedBox(width: 8),
                          Text('Aucun fournisseur disponible sur cette plateforme'),
                        ],
                      ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Boutons d'authentification sociale
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Connexion Sociale',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SocialAuthButtons(
                      userType: _selectedUserType,
                      onAuthResult: _handleAuthResult,
                      isLoading: _isLoading,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Résultat du dernier test
            if (_lastResult != null)
              Card(
                color: _lastResult!.contains('succès') ? Colors.green[50] : Colors.red[50],
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            _lastResult!.contains('succès') ? Icons.check_circle : Icons.error,
                            color: _lastResult!.contains('succès') ? Colors.green : Colors.red,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'Dernier Résultat',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _lastResult!,
                        style: const TextStyle(fontSize: 14),
                      ),
                      const SizedBox(height: 8),
                      TextButton(
                        onPressed: () {
                          setState(() {
                            _lastResult = null;
                          });
                        },
                        child: const Text('Effacer'),
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 20),

            // Boutons de test manuel
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Tests Manuels',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _testGoogleAuth,
                            child: const Text('Test Google'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _testAppleAuth,
                            child: const Text('Test Apple'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserTypeCard(UserType userType) {
    final isSelected = _selectedUserType == userType;
    const primaryColor = Color(0xFFFF7900);

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedUserType = userType;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? primaryColor.withOpacity(0.1) : Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? primaryColor : Colors.grey[300]!,
            width: 2,
          ),
        ),
        child: Column(
          children: [
            Text(
              userType.icon,
              style: const TextStyle(fontSize: 24),
            ),
            const SizedBox(height: 4),
            Text(
              userType.displayName,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: isSelected ? primaryColor : Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _handleAuthResult(Map<String, dynamic> result) {
    setState(() {
      _isLoading = false;
      _lastResult = result['success'] 
          ? 'Connexion réussie ! Utilisateur: ${result['user']?['email_utilisateur'] ?? 'N/A'}'
          : 'Erreur: ${result['message']}';
    });

    if (result['success']) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Authentification réussie !'),
          backgroundColor: Colors.green,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result['message'] ?? 'Erreur d\'authentification'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _testGoogleAuth() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _socialAuthService.signInWithGoogle(
        userType: _selectedUserType,
      );
      _handleAuthResult(result);
    } catch (e) {
      _handleAuthResult({
        'success': false,
        'message': 'Erreur test Google: $e',
      });
    }
  }

  Future<void> _testAppleAuth() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _socialAuthService.signInWithApple(
        userType: _selectedUserType,
      );
      _handleAuthResult(result);
    } catch (e) {
      _handleAuthResult({
        'success': false,
        'message': 'Erreur test Apple: $e',
      });
    }
  }
}
