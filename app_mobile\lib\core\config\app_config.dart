import 'package:flutter/foundation.dart';

/// Configuration centralisée de l'application
/// Gère les paramètres sensibles et les configurations par environnement
class AppConfig {
  // Configuration par défaut (développement)
  static const String _defaultApiBaseUrl = 'http://localhost:3001';
  static const String _defaultMqttBroker = 'localhost';
  static const int _defaultMqttPort = 1883;
  
  // Configuration de production (à définir via variables d'environnement)
  static const String apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: _defaultApiBaseUrl,
  );
  
  static const String mqttBroker = String.fromEnvironment(
    'MQTT_BROKER',
    defaultValue: _defaultMqttBroker,
  );
  
  static const int mqttPort = int.fromEnvironment(
    'MQTT_PORT',
    defaultValue: _defaultMqttPort,
  );
  
  // Configuration TTS
  static const String ttsLanguage = 'fr-FR';
  static const double ttsSpeechRate = 0.8;
  static const double ttsVolume = 1.0;
  static const double ttsPitch = 1.0;
  
  // Configuration des timeouts
  static const Duration apiTimeout = Duration(seconds: 10);
  static const Duration connectionTimeout = Duration(seconds: 5);
  
  // Configuration de cache
  static const Duration cacheExpiration = Duration(hours: 1);
  static const int maxCacheSize = 100; // Nombre d'éléments
  
  // Configuration des commandes vocales
  static const int maxCommandHistory = 50;
  static const Duration commandTimeout = Duration(seconds: 5);
  
  // Configuration de géolocalisation
  static const double locationAccuracyThreshold = 10.0; // mètres
  static const Duration locationUpdateInterval = Duration(seconds: 10);
  
  // Configuration des notifications
  static const int maxNotifications = 100;
  static const Duration notificationExpiration = Duration(days: 7);
  
  // Validation de la configuration
  static bool get isValidConfig {
    return apiBaseUrl.isNotEmpty && 
           mqttBroker.isNotEmpty && 
           mqttPort > 0;
  }
  
  // Informations de debug (uniquement en mode debug)
  static Map<String, dynamic> get debugInfo {
    assert(() {
      // Debug information logging - only executed in debug mode
      debugPrint('AppConfig Debug Info: {apiBaseUrl: $apiBaseUrl, mqttBroker: $mqttBroker, mqttPort: $mqttPort, isValidConfig: $isValidConfig}');
      return true;
    }());
    return {};
  }
}

/// Énumération des environnements
enum Environment {
  development,
  staging,
  production,
}

/// Configuration spécifique par environnement
class EnvironmentConfig {
  static Environment get current {
    const envName = String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
    switch (envName) {
      case 'production':
        return Environment.production;
      case 'staging':
        return Environment.staging;
      case 'development':
      default:
        return Environment.development;
    }
  }
  
  static bool get isDevelopment => current == Environment.development;
  static bool get isStaging => current == Environment.staging;
  static bool get isProduction => current == Environment.production;
  
  // Logging selon l'environnement
  static bool get enableDebugLogs => !isProduction;
  static bool get enableAnalytics => isProduction || isStaging;
  static bool get enableCrashReporting => isProduction || isStaging;
}
