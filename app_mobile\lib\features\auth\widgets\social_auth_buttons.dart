import 'package:flutter/material.dart';
import '../../../shared/models/user.dart';
import '../services/social_auth_service.dart';

/// Widget pour les boutons d'authentification sociale
class SocialAuthButtons extends StatefulWidget {
  final UserType userType;
  final Function(Map<String, dynamic>) onAuthResult;
  final bool isLoading;

  const SocialAuthButtons({
    super.key,
    required this.userType,
    required this.onAuthResult,
    this.isLoading = false,
  });

  @override
  State<SocialAuthButtons> createState() => _SocialAuthButtonsState();
}

class _SocialAuthButtonsState extends State<SocialAuthButtons> {
  final SocialAuthService _socialAuthService = SocialAuthService();
  String? _loadingProvider;

  @override
  Widget build(BuildContext context) {
    final availableProviders = _socialAuthService.getAvailableProviders();

    if (availableProviders.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        // Séparateur
        Row(
          children: [
            Expanded(child: Divider(color: Colors.grey[300])),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Ou connectez-vous avec',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            ),
            Expanded(child: Divider(color: Colors.grey[300])),
          ],
        ),

        const SizedBox(height: 20),

        // Boutons d'authentification sociale
        ...availableProviders.map((provider) => Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildSocialButton(provider),
        )),
      ],
    );
  }

  Widget _buildSocialButton(SocialProvider provider) {
    final isLoading = _loadingProvider == provider.name || widget.isLoading;
    
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: isLoading ? null : () => _handleSocialAuth(provider),
        style: ElevatedButton.styleFrom(
          backgroundColor: _getProviderColor(provider),
          foregroundColor: _getProviderTextColor(provider),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(
              color: _getProviderBorderColor(provider),
              width: 1,
            ),
          ),
        ),
        child: isLoading
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getProviderTextColor(provider),
                  ),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildProviderIcon(provider),
                  const SizedBox(width: 12),
                  Text(
                    'Continuer avec ${provider.displayName}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildProviderIcon(SocialProvider provider) {
    switch (provider) {
      case SocialProvider.google:
        return Container(
          width: 20,
          height: 20,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: NetworkImage('https://developers.google.com/identity/images/g-logo.png'),
              fit: BoxFit.contain,
            ),
          ),
        );
      case SocialProvider.apple:
        return const Icon(
          Icons.apple,
          size: 20,
          color: Colors.white,
        );
      case SocialProvider.facebook:
        return Container(
          width: 20,
          height: 20,
          decoration: const BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
          ),
          child: const Center(
            child: Text(
              'f',
              style: TextStyle(
                color: Color(0xFF1877F2),
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      case SocialProvider.microsoft:
        return Container(
          width: 20,
          height: 20,
          child: Image.network(
            'https://upload.wikimedia.org/wikipedia/commons/4/44/Microsoft_logo.svg',
            fit: BoxFit.contain,
          ),
        );
    }
  }

  Color _getProviderColor(SocialProvider provider) {
    switch (provider) {
      case SocialProvider.google:
        return Colors.white;
      case SocialProvider.apple:
        return Colors.black;
      case SocialProvider.facebook:
        return const Color(0xFF1877F2);
      case SocialProvider.microsoft:
        return const Color(0xFF00A4EF);
    }
  }

  Color _getProviderTextColor(SocialProvider provider) {
    switch (provider) {
      case SocialProvider.google:
        return Colors.black87;
      case SocialProvider.apple:
        return Colors.white;
      case SocialProvider.facebook:
        return Colors.white;
      case SocialProvider.microsoft:
        return Colors.white;
    }
  }

  Color _getProviderBorderColor(SocialProvider provider) {
    switch (provider) {
      case SocialProvider.google:
        return Colors.grey[300]!;
      case SocialProvider.apple:
        return Colors.black;
      case SocialProvider.facebook:
        return const Color(0xFF1877F2);
      case SocialProvider.microsoft:
        return const Color(0xFF00A4EF);
    }
  }

  Future<void> _handleSocialAuth(SocialProvider provider) async {
    setState(() {
      _loadingProvider = provider.name;
    });

    try {
      Map<String, dynamic> result;

      switch (provider) {
        case SocialProvider.google:
          result = await _socialAuthService.signInWithGoogle(
            userType: widget.userType,
          );
          break;
        case SocialProvider.apple:
          result = await _socialAuthService.signInWithApple(
            userType: widget.userType,
          );
          break;
        case SocialProvider.facebook:
          result = {
            'success': false,
            'message': 'Facebook Sign-In sera bientôt disponible',
          };
          break;
        case SocialProvider.microsoft:
          result = {
            'success': false,
            'message': 'Microsoft Sign-In sera bientôt disponible',
          };
          break;
      }

      widget.onAuthResult(result);

    } catch (e) {
      widget.onAuthResult({
        'success': false,
        'message': 'Erreur lors de l\'authentification: $e',
      });
    } finally {
      if (mounted) {
        setState(() {
          _loadingProvider = null;
        });
      }
    }
  }
}

/// Widget pour un bouton d'authentification sociale personnalisé
class CustomSocialButton extends StatelessWidget {
  final String text;
  final Widget icon;
  final Color backgroundColor;
  final Color textColor;
  final Color borderColor;
  final VoidCallback? onPressed;
  final bool isLoading;

  const CustomSocialButton({
    super.key,
    required this.text,
    required this.icon,
    required this.backgroundColor,
    required this.textColor,
    required this.borderColor,
    this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: textColor,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(color: borderColor, width: 1),
          ),
        ),
        child: isLoading
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(textColor),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  icon,
                  const SizedBox(width: 12),
                  Text(
                    text,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
