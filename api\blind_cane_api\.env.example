# Configuration de la base de données MySQL
DB_HOST=localhost
DB_PORT=3306
DB_NAME=blind_cane_db
DB_USER=root
DB_PASSWORD=your_password

# Configuration JWT
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# Configuration du serveur
PORT=3001
NODE_ENV=development

# Configuration CORS
CORS_ORIGIN=http://localhost:3000,http://***************:3000

# Configuration de sécurité
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Configuration de logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
