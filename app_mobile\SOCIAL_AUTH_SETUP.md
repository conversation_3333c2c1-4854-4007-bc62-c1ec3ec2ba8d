# Configuration de l'Authentification Sociale

Ce guide explique comment configurer l'authentification avec Google, Apple et d'autres fournisseurs dans l'application Canne Connectée.

## 🚀 Fonctionnalités Implémentées

### ✅ Authentification Google
- Connexion avec compte Google
- Récupération automatique du profil utilisateur
- Gestion des tokens d'accès et d'identité

### ✅ Authentification Apple (iOS uniquement)
- Sign in with Apple
- Support des identifiants masqués
- Gestion sécurisée des tokens

### 🔄 En Développement
- Facebook Login
- Microsoft Account
- Authentification par SMS

## 📱 Configuration Mobile

### 1. Dépendances Ajoutées

```yaml
dependencies:
  # Authentification sociale
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^6.1.2
  firebase_auth: ^5.3.1
  firebase_core: ^3.6.0
  crypto: ^3.0.5
```

### 2. Structure des Fichiers

```
lib/features/auth/
├── services/
│   └── social_auth_service.dart     # Service d'authentification sociale
├── widgets/
│   └── social_auth_buttons.dart     # Boutons d'authentification
└── pages/
    └── enhanced_login_page.dart     # Page de connexion améliorée
```

### 3. Configuration Android

#### Fichier `android/app/build.gradle`
```gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
    }
}

dependencies {
    implementation 'com.google.android.gms:play-services-auth:20.7.0'
}
```

#### Fichier `android/app/google-services.json`
- Téléchargez depuis la Console Firebase
- Placez dans `android/app/`
- Configurez avec votre projet Firebase

### 4. Configuration iOS

#### Fichier `ios/Runner/Info.plist`
```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>REVERSED_CLIENT_ID</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>YOUR_REVERSED_CLIENT_ID</string>
        </array>
    </dict>
</array>
```

## 🔧 Configuration Backend

### 1. Base de Données

Ajoutez ces colonnes à la table `utilisateurs` :

```sql
ALTER TABLE utilisateurs 
ADD COLUMN social_provider VARCHAR(50) NULL,
ADD COLUMN social_provider_id VARCHAR(255) NULL,
ADD COLUMN photo_url TEXT NULL,
ADD COLUMN derniere_connexion TIMESTAMP NULL;

-- Index pour améliorer les performances
CREATE INDEX idx_social_provider ON utilisateurs(social_provider, social_provider_id);
CREATE INDEX idx_email ON utilisateurs(email_utilisateur);
```

### 2. Route d'Authentification

```javascript
// POST /api/auth/social-login
{
  "provider": "google",
  "provider_id": "123456789",
  "email": "<EMAIL>",
  "display_name": "John Doe",
  "photo_url": "https://...",
  "user_type": "aveugle",
  "access_token": "...",
  "id_token": "..."
}
```

### 3. Réponse du Serveur

```javascript
{
  "success": true,
  "message": "Connexion réussie",
  "token": "jwt_token_here",
  "user": {
    "id_utilisateur": 1,
    "nom_utilisateur": "Doe",
    "prenom_utilisateur": "John",
    "email_utilisateur": "<EMAIL>",
    "user_type": "aveugle",
    "photo_url": "https://...",
    "social_provider": "google"
  }
}
```

## 🔐 Configuration des Fournisseurs

### Google OAuth

1. **Console Google Cloud**
   - Créez un projet
   - Activez l'API Google Sign-In
   - Configurez l'écran de consentement OAuth
   - Créez des identifiants OAuth 2.0

2. **Configuration Firebase**
   - Ajoutez votre projet à Firebase
   - Activez l'authentification Google
   - Téléchargez les fichiers de configuration

### Apple Sign-In

1. **Apple Developer Console**
   - Activez Sign in with Apple
   - Configurez les identifiants
   - Ajoutez les domaines autorisés

2. **Configuration Xcode**
   - Ajoutez la capacité Sign in with Apple
   - Configurez les entitlements

## 🎯 Utilisation

### Page de Connexion Améliorée

```dart
// Navigation vers la page de connexion
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const EnhancedLoginPage(),
  ),
);
```

### Boutons d'Authentification Sociale

```dart
SocialAuthButtons(
  userType: UserType.aveugle,
  onAuthResult: (result) {
    if (result['success']) {
      // Connexion réussie
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => HomePage()),
      );
    } else {
      // Afficher l'erreur
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(result['message'])),
      );
    }
  },
)
```

## 🔍 Débogage

### Logs Utiles

```dart
// Activer les logs détaillés
flutter run --debug
```

### Erreurs Communes

1. **Google Sign-In échoue**
   - Vérifiez le SHA-1 dans Firebase
   - Vérifiez le package name
   - Assurez-vous que google-services.json est correct

2. **Apple Sign-In non disponible**
   - Disponible uniquement sur iOS 13+
   - Vérifiez les entitlements
   - Testez sur un appareil physique

3. **Token invalide**
   - Vérifiez la configuration du serveur
   - Assurez-vous que les clés sont correctes
   - Vérifiez l'expiration des tokens

## 📚 Ressources

- [Google Sign-In Documentation](https://developers.google.com/identity/sign-in/android)
- [Apple Sign-In Documentation](https://developer.apple.com/sign-in-with-apple/)
- [Firebase Auth Documentation](https://firebase.google.com/docs/auth)

## 🔄 Prochaines Étapes

1. **Facebook Login**
   - Ajouter le SDK Facebook
   - Configurer l'application Facebook
   - Implémenter l'authentification

2. **Microsoft Account**
   - Intégrer MSAL (Microsoft Authentication Library)
   - Configurer Azure AD

3. **Authentification par SMS**
   - Implémenter la vérification OTP
   - Intégrer un service SMS (Twilio, etc.)

4. **Authentification Biométrique**
   - Empreinte digitale
   - Reconnaissance faciale
   - Touch ID / Face ID
