const mysql = require('mysql2/promise');
require('dotenv').config();

/**
 * Script d'initialisation de la base de données blind_cane_db
 * Crée toutes les tables nécessaires pour l'application
 */

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  charset: 'utf8mb4'
};

const DATABASE_NAME = process.env.DB_NAME || 'blind_cane_db';

const createDatabaseSQL = `CREATE DATABASE IF NOT EXISTS ${DATABASE_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;`;

const createTablesSQL = [
  // Table des utilisateurs
  `CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nom_utilisateur VARCHAR(100) NOT NULL,
    prenom_utilisateur VARCHAR(100) NOT NULL,
    email_utilisateur VARCHAR(255) UNIQUE NOT NULL,
    mot_de_passe VARCHAR(255) NOT NULL,
    contact_utilisateur VARCHAR(20),
    adresse_utilisateur TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email_utilisateur),
    INDEX idx_active (is_active)
  ) ENGINE=InnoDB;`,

  // Table des proches/aidants
  `CREATE TABLE IF NOT EXISTS proches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    telephone VARCHAR(20),
    relation_type ENUM('famille', 'ami', 'professionnel', 'autre') DEFAULT 'autre',
    priority ENUM('haute', 'normale', 'basse') DEFAULT 'normale',
    can_receive_alerts BOOLEAN DEFAULT TRUE,
    can_track_location BOOLEAN DEFAULT TRUE,
    can_receive_calls BOOLEAN DEFAULT TRUE,
    status ENUM('pending', 'accepted', 'declined', 'blocked') DEFAULT 'pending',
    invited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    accepted_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    UNIQUE KEY unique_user_email (user_id, email)
  ) ENGINE=InnoDB;`,

  // Table des alertes SOS
  `CREATE TABLE IF NOT EXISTS alertes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    type_alerte ENUM('sos', 'chute', 'panique', 'medical', 'autre') NOT NULL,
    message_alerte TEXT,
    status_alerte ENUM('active', 'resolved', 'cancelled') DEFAULT 'active',
    niveau_urgence ENUM('faible', 'moyen', 'eleve', 'critique') DEFAULT 'moyen',
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    adresse_approximative TEXT,
    resolved_at TIMESTAMP NULL,
    resolved_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (resolved_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status_alerte),
    INDEX idx_created_at (created_at),
    INDEX idx_location (latitude, longitude)
  ) ENGINE=InnoDB;`,

  // Table des contacts
  `CREATE TABLE IF NOT EXISTS contacts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100),
    telephone VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    relation ENUM('famille', 'ami', 'professionnel', 'urgence', 'autre') DEFAULT 'autre',
    is_favorite BOOLEAN DEFAULT FALSE,
    is_emergency BOOLEAN DEFAULT FALSE,
    photo_url VARCHAR(500),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_favorite (is_favorite),
    INDEX idx_emergency (is_emergency),
    INDEX idx_nom (nom)
  ) ENGINE=InnoDB;`,

  // Table des messages
  `CREATE TABLE IF NOT EXISTS messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    contact_id INT,
    telephone_destinataire VARCHAR(20) NOT NULL,
    contenu TEXT NOT NULL,
    type_message ENUM('sms', 'vocal', 'urgence') DEFAULT 'sms',
    direction ENUM('entrant', 'sortant') NOT NULL,
    status ENUM('envoye', 'recu', 'lu', 'echec') DEFAULT 'envoye',
    is_read BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_contact_id (contact_id),
    INDEX idx_telephone (telephone_destinataire),
    INDEX idx_sent_at (sent_at),
    INDEX idx_is_read (is_read)
  ) ENGINE=InnoDB;`,

  // Table des appels
  `CREATE TABLE IF NOT EXISTS appels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    contact_id INT,
    telephone VARCHAR(20) NOT NULL,
    type_appel ENUM('entrant', 'sortant', 'manque') NOT NULL,
    duree_secondes INT DEFAULT 0,
    status ENUM('repondu', 'manque', 'rejete', 'occupe') DEFAULT 'manque',
    is_emergency BOOLEAN DEFAULT FALSE,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_contact_id (contact_id),
    INDEX idx_telephone (telephone),
    INDEX idx_started_at (started_at),
    INDEX idx_type_appel (type_appel)
  ) ENGINE=InnoDB;`,

  // Table des trajets/parcours
  `CREATE TABLE IF NOT EXISTS trajets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    nom_trajet VARCHAR(200),
    origine_latitude DECIMAL(10, 8),
    origine_longitude DECIMAL(11, 8),
    origine_adresse TEXT,
    destination_latitude DECIMAL(10, 8),
    destination_longitude DECIMAL(11, 8),
    destination_adresse TEXT,
    distance_metres INT,
    duree_secondes INT,
    status ENUM('en_cours', 'termine', 'annule') DEFAULT 'en_cours',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at)
  ) ENGINE=InnoDB;`,

  // Table des positions GPS (pour le suivi en temps réel)
  `CREATE TABLE IF NOT EXISTS positions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    trajet_id INT,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    altitude DECIMAL(8, 2),
    precision_metres DECIMAL(6, 2),
    vitesse_kmh DECIMAL(5, 2),
    direction_degres DECIMAL(5, 2),
    timestamp_gps TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (trajet_id) REFERENCES trajets(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_trajet_id (trajet_id),
    INDEX idx_timestamp (timestamp_gps),
    INDEX idx_location (latitude, longitude)
  ) ENGINE=InnoDB;`,

  // Table des notifications
  `CREATE TABLE IF NOT EXISTS notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    type_notification ENUM('alerte', 'message', 'appel', 'systeme', 'proche') NOT NULL,
    titre VARCHAR(200) NOT NULL,
    contenu TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    priority ENUM('basse', 'normale', 'haute', 'critique') DEFAULT 'normale',
    action_url VARCHAR(500),
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at),
    INDEX idx_priority (priority)
  ) ENGINE=InnoDB;`
];

async function initializeDatabase() {
  let connection;
  
  try {
    console.log('🚀 Initialisation de la base de données blind_cane_db...\n');
    
    // Connexion sans spécifier de base de données
    connection = await mysql.createConnection(dbConfig);
    
    // Créer la base de données
    console.log('📊 Création de la base de données...');
    await connection.execute(createDatabaseSQL);
    console.log('✅ Base de données créée ou existe déjà\n');
    
    // Se connecter à la base de données
    await connection.changeUser({ database: DATABASE_NAME });
    
    // Créer les tables
    console.log('📋 Création des tables...');
    for (let i = 0; i < createTablesSQL.length; i++) {
      const tableName = createTablesSQL[i].match(/CREATE TABLE IF NOT EXISTS (\w+)/)[1];
      console.log(`  📄 Création de la table: ${tableName}`);
      await connection.execute(createTablesSQL[i]);
    }
    
    console.log('\n✅ Toutes les tables ont été créées avec succès !');
    console.log('\n📊 Structure de la base de données:');
    console.log('  👤 users - Utilisateurs de l\'application');
    console.log('  👥 proches - Proches/aidants des utilisateurs');
    console.log('  🚨 alertes - Alertes SOS et urgences');
    console.log('  📞 contacts - Carnet d\'adresses');
    console.log('  💬 messages - Messages SMS/vocaux');
    console.log('  📱 appels - Historique des appels');
    console.log('  🗺️  trajets - Parcours et navigation');
    console.log('  📍 positions - Positions GPS temps réel');
    console.log('  🔔 notifications - Notifications système');
    
    console.log('\n🎉 Base de données initialisée avec succès !');
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  initializeDatabase();
}

module.exports = { initializeDatabase };
