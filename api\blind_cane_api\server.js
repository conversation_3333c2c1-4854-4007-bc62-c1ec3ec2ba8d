const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const { initializeDatabase } = require('./config/database');

// Import des routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const alerteRoutes = require('./routes/alertes');
const contactRoutes = require('./routes/contacts');
const messageRoutes = require('./routes/messages');
const appelRoutes = require('./routes/appels');
const trajetRoutes = require('./routes/trajets');
const notificationRoutes = require('./routes/notifications');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware de sécurité
app.use(helmet());
app.use(compression());

// Configuration CORS
const corsOptions = {
  origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['http://localhost:3000'],
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: {
    error: 'Trop de requêtes depuis cette IP, réessayez plus tard.'
  }
});
app.use(limiter);

// Logging
if (process.env.NODE_ENV !== 'production') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Parsing du body
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Route de santé
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    database: 'blind_cane_db'
  });
});

// Routes API
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/alertes', alerteRoutes);
app.use('/api/contacts', contactRoutes);
app.use('/api/messages', messageRoutes);
app.use('/api/appels', appelRoutes);
app.use('/api/trajets', trajetRoutes);
app.use('/api/notifications', notificationRoutes);

// Route par défaut
app.get('/', (req, res) => {
  res.json({
    message: 'API Canne Connectée - Serveur opérationnel',
    version: '1.0.0',
    database: 'blind_cane_db',
    endpoints: {
      health: '/health',
      auth: '/api/auth',
      users: '/api/users',
      alertes: '/api/alertes',
      contacts: '/api/contacts',
      messages: '/api/messages',
      appels: '/api/appels',
      trajets: '/api/trajets',
      notifications: '/api/notifications'
    }
  });
});

// Middleware de gestion d'erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  
  // Erreur de validation
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      error: 'Erreur de validation',
      details: err.message
    });
  }
  
  // Erreur JWT
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      error: 'Token invalide'
    });
  }
  
  // Erreur de base de données
  if (err.code && err.code.startsWith('ER_')) {
    return res.status(500).json({
      error: 'Erreur de base de données',
      message: process.env.NODE_ENV === 'development' ? err.message : 'Erreur interne'
    });
  }
  
  // Erreur générique
  res.status(err.status || 500).json({
    error: err.message || 'Erreur interne du serveur',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
});

// Route 404
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route non trouvée',
    path: req.originalUrl,
    method: req.method
  });
});

// Initialisation du serveur
async function startServer() {
  try {
    console.log('🚀 Démarrage du serveur API Canne Connectée...\n');
    
    // Initialiser la base de données
    await initializeDatabase();
    console.log('✅ Base de données initialisée\n');
    
    // Démarrer le serveur
    app.listen(PORT, () => {
      console.log('🌟 ================================');
      console.log('🎯 API CANNE CONNECTÉE DÉMARRÉE');
      console.log('🌟 ================================');
      console.log(`🌐 Serveur: http://localhost:${PORT}`);
      console.log(`📊 Base de données: blind_cane_db`);
      console.log(`🔧 Environnement: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📋 Endpoints disponibles:`);
      console.log(`   • GET  /health - État du serveur`);
      console.log(`   • POST /api/auth/login - Connexion`);
      console.log(`   • POST /api/auth/register - Inscription`);
      console.log(`   • GET  /api/alertes - Liste des alertes`);
      console.log(`   • POST /api/alertes - Créer une alerte`);
      console.log(`   • GET  /api/contacts - Liste des contacts`);
      console.log(`   • POST /api/contacts - Ajouter un contact`);
      console.log('🌟 ================================\n');
    });
    
  } catch (error) {
    console.error('❌ Erreur lors du démarrage:', error);
    process.exit(1);
  }
}

// Gestion propre de l'arrêt
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du serveur...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt du serveur...');
  process.exit(0);
});

// Démarrer le serveur
startServer();

module.exports = app;
