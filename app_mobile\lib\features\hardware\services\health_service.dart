import 'dart:async';
import 'dart:math';
import 'package:canne_connectee/features/communication/services/mqtt_service.dart';
import 'package:flutter/foundation.dart';
import 'package:sensors_plus/sensors_plus.dart';
import 'package:battery_plus/battery_plus.dart';

class HealthData {
  final int heartRate;
  final int steps;
  final double temperature;
  final String? bloodPressure;
  final DateTime timestamp;

  HealthData({
    required this.heartRate,
    required this.steps,
    required this.temperature,
    this.bloodPressure,
    required this.timestamp,
  });
}

class HealthService {
  static final HealthService _instance = HealthService._internal();
  factory HealthService() => _instance;
  HealthService._internal();

  final MqttService _mqttService = MqttService();
  final Battery _battery = Battery();
  
  StreamSubscription<AccelerometerEvent>? _accelerometerSubscription;
  StreamSubscription<GyroscopeEvent>? _gyroscopeSubscription;
  
  // Données simulées pour la démo
  int _currentHeartRate = 75;
  int _dailySteps = 0;
  int _totalSteps = 0;
  double _currentTemperature = 36.5;
  bool _isMonitoring = false;
  
  // Détection de chute
  bool _fallDetectionEnabled = true;
  double _fallThreshold = 15.0; // m/s²
  DateTime? _lastFallAlert;
  
  // Compteur de pas
  double _lastAcceleration = 0;
  int _stepsSinceLastUpdate = 0;
  DateTime _lastStepUpdate = DateTime.now();

  bool get isMonitoring => _isMonitoring;
  int get currentHeartRate => _currentHeartRate;
  int get dailySteps => _dailySteps;
  int get totalSteps => _totalSteps;
  double get currentTemperature => _currentTemperature;

  Future<void> initialize() async {
    await _startSensorMonitoring();
    await _startBatteryMonitoring();
    _startHealthSimulation();
    
    if (kDebugMode) {
      print('HealthService: Initialisé avec succès');
    }
  }

  Future<void> _startSensorMonitoring() async {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    
    // Surveillance de l'accéléromètre pour la détection de chute et comptage de pas
    _accelerometerSubscription = accelerometerEvents.listen((AccelerometerEvent event) {
      final acceleration = sqrt(event.x * event.x + event.y * event.y + event.z * event.z);
      
      // Détection de chute
      if (_fallDetectionEnabled && acceleration > _fallThreshold) {
        _detectFall(acceleration);
      }
      
      // Comptage de pas (algorithme simple)
      _countSteps(acceleration);
      
      _lastAcceleration = acceleration;
    });

    // Surveillance du gyroscope pour des données supplémentaires
    _gyroscopeSubscription = gyroscopeEvents.listen((GyroscopeEvent event) {
      // Peut être utilisé pour améliorer la détection de chute
    });
  }

  void _detectFall(double acceleration) {
    final now = DateTime.now();
    
    // Éviter les alertes répétées (minimum 30 secondes entre les alertes)
    if (_lastFallAlert != null && 
        now.difference(_lastFallAlert!).inSeconds < 30) {
      return;
    }
    
    _lastFallAlert = now;
    
    // Publier l'alerte de chute via MQTT
    _mqttService.publishFallDetection(
      latitude: 0.0, // À remplacer par la vraie position GPS
      longitude: 0.0,
      accelerationMagnitude: acceleration,
      severity: acceleration > 20.0 ? 'severe' : 'moderate',
    );
    
    if (kDebugMode) {
      print('⚠️ Chute détectée ! Accélération: ${acceleration.toStringAsFixed(2)} m/s²');
    }
  }

  void _countSteps(double acceleration) {
    final now = DateTime.now();
    
    // Algorithme simple de détection de pas
    if (acceleration > 10.5 && acceleration < 13.0 && 
        _lastAcceleration < 10.0 &&
        now.difference(_lastStepUpdate).inMilliseconds > 300) {
      
      _stepsSinceLastUpdate++;
      _dailySteps++;
      _totalSteps++;
      _lastStepUpdate = now;
      
      // Publier les pas toutes les 10 étapes
      if (_stepsSinceLastUpdate >= 10) {
        _publishStepsData();
        _stepsSinceLastUpdate = 0;
      }
    }
  }

  void _publishStepsData() {
    final distance = _dailySteps * 0.7; // Estimation: 70cm par pas
    final calories = (_dailySteps * 0.04).round(); // Estimation: 0.04 cal par pas
    
    _mqttService.publishStepsCount(
      totalSteps: _totalSteps,
      dailySteps: _dailySteps,
      distance: distance,
      calories: calories,
    );
  }

  Future<void> _startBatteryMonitoring() async {
    // Publier le niveau de batterie toutes les 5 minutes
    Timer.periodic(const Duration(minutes: 5), (timer) async {
      try {
        final batteryLevel = await _battery.batteryLevel;
        final batteryState = await _battery.batteryState;
        final isCharging = batteryState == BatteryState.charging;
        
        _mqttService.publishBatteryLevel(
          batteryLevel: batteryLevel,
          isCharging: isCharging,
          estimatedHours: _calculateBatteryLife(batteryLevel, isCharging),
        );
      } catch (e) {
        if (kDebugMode) {
          print('Erreur surveillance batterie: $e');
        }
      }
    });
  }

  int? _calculateBatteryLife(int batteryLevel, bool isCharging) {
    if (isCharging) return null;
    
    // Estimation simple basée sur le niveau actuel
    // Supposons 24h d'autonomie à 100%
    return ((batteryLevel / 100) * 24).round();
  }

  void _startHealthSimulation() {
    // Simulation des données de santé (à remplacer par de vrais capteurs)
    Timer.periodic(const Duration(minutes: 1), (timer) {
      _simulateHeartRate();
      _simulateTemperature();
      _publishHealthData();
    });
  }

  void _simulateHeartRate() {
    // Simulation d'une fréquence cardiaque réaliste
    final random = Random();
    final baseRate = 75;
    final variation = random.nextInt(20) - 10; // ±10 bpm
    _currentHeartRate = (baseRate + variation).clamp(60, 100);
    
    // Publier la fréquence cardiaque
    _mqttService.publishHeartRate(
      heartRate: _currentHeartRate,
      context: _getHeartRateContext(),
    );
  }

  String _getHeartRateContext() {
    if (_currentHeartRate < 70) return 'rest';
    if (_currentHeartRate < 85) return 'walking';
    if (_currentHeartRate < 100) return 'exercise';
    return 'stress';
  }

  void _simulateTemperature() {
    // Simulation d'une température corporelle réaliste
    final random = Random();
    final baseTemp = 36.5;
    final variation = (random.nextDouble() - 0.5) * 1.0; // ±0.5°C
    _currentTemperature = (baseTemp + variation).clamp(35.0, 38.0);
  }

  void _publishHealthData() {
    _mqttService.publishHealthData(
      heartRate: _currentHeartRate,
      steps: _dailySteps,
      temperature: _currentTemperature,
      bloodPressure: _generateBloodPressure(),
    );
  }

  String _generateBloodPressure() {
    // Simulation de tension artérielle
    final random = Random();
    final systolic = 120 + random.nextInt(20); // 120-140
    final diastolic = 80 + random.nextInt(10);  // 80-90
    return '$systolic/$diastolic';
  }

  HealthData getCurrentHealthData() {
    return HealthData(
      heartRate: _currentHeartRate,
      steps: _dailySteps,
      temperature: _currentTemperature,
      bloodPressure: _generateBloodPressure(),
      timestamp: DateTime.now(),
    );
  }

  void enableFallDetection(bool enabled) {
    _fallDetectionEnabled = enabled;
    if (kDebugMode) {
      print('Détection de chute: ${enabled ? 'activée' : 'désactivée'}');
    }
  }

  void setFallThreshold(double threshold) {
    _fallThreshold = threshold;
    if (kDebugMode) {
      print('Seuil de détection de chute: ${threshold.toStringAsFixed(1)} m/s²');
    }
  }

  void resetDailySteps() {
    _dailySteps = 0;
    _stepsSinceLastUpdate = 0;
    if (kDebugMode) {
      print('Compteur de pas quotidien remis à zéro');
    }
  }

  void dispose() {
    _accelerometerSubscription?.cancel();
    _gyroscopeSubscription?.cancel();
    _isMonitoring = false;
  }
}
