import 'dart:convert';
import 'package:canne_connectee/features/auth/services/auth_service.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/proche.dart';

class ProcheService {
  static final ProcheService _instance = ProcheService._internal();
  factory ProcheService() => _instance;
  ProcheService._internal();

  static const String _baseUrl = 'http://192.168.252.246:3000/';
  final AuthService _authService = AuthService();

  List<Proche> _proches = [];
  bool _isLoading = false;

  List<Proche> get proches => List.unmodifiable(_proches);
  bool get isLoading => _isLoading;

  /// Récupérer tous les proches de l'utilisateur connecté
  Future<Map<String, dynamic>> getProches() async {
    if (!_authService.isLoggedIn) {
      return {
        'success': false,
        'message': 'Utilisateur non connecté',
      };
    }

    _isLoading = true;

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/proches'),
        headers: _authService.getAuthHeaders(),
      );

      final data = json.decode(response.body);

      if (response.statusCode == 200) {
        final List<dynamic> prochesData = data['proches'] ?? [];
        _proches = prochesData.map((json) => Proche.fromJson(json)).toList();

        if (kDebugMode) {
          print('ProcheService: ${_proches.length} proches récupérés');
        }

        return {
          'success': true,
          'proches': _proches,
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Erreur lors de la récupération des proches',
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('ProcheService: Erreur récupération proches: $e');
      }
      return {
        'success': false,
        'message': 'Erreur de connexion',
      };
    } finally {
      _isLoading = false;
    }
  }

  /// Inviter un proche
  Future<Map<String, dynamic>> inviterProche({
    required String nom,
    required String prenom,
    required String email,
    String? telephone,
    required RelationType relationType,
    ProchePriority priority = ProchePriority.normale,
    bool canReceiveAlerts = true,
    bool canTrackLocation = true,
    bool canReceiveCalls = true,
  }) async {
    if (!_authService.isLoggedIn) {
      return {
        'success': false,
        'message': 'Utilisateur non connecté',
      };
    }

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/proches/invite'),
        headers: _authService.getAuthHeaders(),
        body: json.encode({
          'nom': nom,
          'prenom': prenom,
          'email': email,
          'telephone': telephone,
          'relationType': relationType.toString().split('.').last,
          'priority': priority.toString().split('.').last,
          'canReceiveAlerts': canReceiveAlerts,
          'canTrackLocation': canTrackLocation,
          'canReceiveCalls': canReceiveCalls,
        }),
      );

      final data = json.decode(response.body);

      if (response.statusCode == 201) {
        final nouveauProche = Proche.fromJson(data['proche']);
        _proches.add(nouveauProche);

        if (kDebugMode) {
          print('ProcheService: Invitation envoyée à ${nouveauProche.fullName}');
        }

        return {
          'success': true,
          'proche': nouveauProche,
          'message': 'Invitation envoyée avec succès',
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Erreur lors de l\'envoi de l\'invitation',
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('ProcheService: Erreur invitation proche: $e');
      }
      return {
        'success': false,
        'message': 'Erreur de connexion',
      };
    }
  }

  /// Mettre à jour les permissions d'un proche
  Future<Map<String, dynamic>> updateProchePermissions({
    required String procheId,
    bool? canReceiveAlerts,
    bool? canTrackLocation,
    bool? canReceiveCalls,
    ProchePriority? priority,
  }) async {
    if (!_authService.isLoggedIn) {
      return {
        'success': false,
        'message': 'Utilisateur non connecté',
      };
    }

    try {
      final response = await http.put(
        Uri.parse('$_baseUrl/proches/$procheId/permissions'),
        headers: _authService.getAuthHeaders(),
        body: json.encode({
          if (canReceiveAlerts != null) 'can_receive_alerts': canReceiveAlerts,
          if (canTrackLocation != null) 'can_track_location': canTrackLocation,
          if (canReceiveCalls != null) 'can_receive_calls': canReceiveCalls,
          if (priority != null) 'priority': priority.toString().split('.').last,
        }),
      );

      final data = json.decode(response.body);

      if (response.statusCode == 200) {
        final procheUpdated = Proche.fromJson(data['proche']);
        final index = _proches.indexWhere((p) => p.id == procheId);
        if (index != -1) {
          _proches[index] = procheUpdated;
        }

        return {
          'success': true,
          'proche': procheUpdated,
          'message': 'Permissions mises à jour',
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Erreur lors de la mise à jour',
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('ProcheService: Erreur mise à jour permissions: $e');
      }
      return {
        'success': false,
        'message': 'Erreur de connexion',
      };
    }
  }

  /// Supprimer un proche
  Future<Map<String, dynamic>> supprimerProche(String procheId) async {
    if (!_authService.isLoggedIn) {
      return {
        'success': false,
        'message': 'Utilisateur non connecté',
      };
    }

    try {
      final response = await http.delete(
        Uri.parse('$_baseUrl/proches/$procheId'),
        headers: _authService.getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        _proches.removeWhere((p) => p.id == procheId);

        if (kDebugMode) {
          print('ProcheService: Proche supprimé: $procheId');
        }

        return {
          'success': true,
          'message': 'Proche supprimé avec succès',
        };
      } else {
        final data = json.decode(response.body);
        return {
          'success': false,
          'message': data['message'] ?? 'Erreur lors de la suppression',
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('ProcheService: Erreur suppression proche: $e');
      }
      return {
        'success': false,
        'message': 'Erreur de connexion',
      };
    }
  }

  /// Renvoyer une invitation
  Future<Map<String, dynamic>> renvoyerInvitation(String procheId) async {
    if (!_authService.isLoggedIn) {
      return {
        'success': false,
        'message': 'Utilisateur non connecté',
      };
    }

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/proches/$procheId/resend-invitation'),
        headers: _authService.getAuthHeaders(),
      );

      final data = json.decode(response.body);

      if (response.statusCode == 200) {
        final procheUpdated = Proche.fromJson(data['proche']);
        final index = _proches.indexWhere((p) => p.id == procheId);
        if (index != -1) {
          _proches[index] = procheUpdated;
        }

        return {
          'success': true,
          'proche': procheUpdated,
          'message': 'Invitation renvoyée',
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Erreur lors du renvoi',
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('ProcheService: Erreur renvoi invitation: $e');
      }
      return {
        'success': false,
        'message': 'Erreur de connexion',
      };
    }
  }

  /// Obtenir les proches par priorité
  List<Proche> getProchesByPriority(ProchePriority priority) {
    return _proches.where((p) => p.priority == priority && p.isActive).toList();
  }

  /// Obtenir les proches connectés
  List<Proche> getConnectedProches() {
    return _proches.where((p) => p.isConnected).toList();
  }

  /// Obtenir les invitations en attente
  List<Proche> getPendingInvitations() {
    return _proches.where((p) => p.isPending).toList();
  }

  /// Nettoyer les données
  void clear() {
    _proches.clear();
    _isLoading = false;
  }
}
