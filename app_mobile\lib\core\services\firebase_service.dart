import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

/// Service d'initialisation Firebase
class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  bool _isInitialized = false;

  bool get isInitialized => _isInitialized;

  /// Initialise Firebase
  Future<void> initialize() async {
    try {
      if (_isInitialized) {
        if (kDebugMode) {
          print('FirebaseService: Déjà initialisé');
        }
        return;
      }

      if (kDebugMode) {
        print('FirebaseService: Initialisation en cours...');
      }

      // Configuration Firebase pour différentes plateformes
      const firebaseOptions = FirebaseOptions(
        apiKey: 'AIzaSyABCDEFGHIJKLMNOPQRSTUVWXYZ1234567',
        appId: '1:123456789012:android:abcdef123456789',
        messagingSenderId: '123456789012',
        projectId: 'canne-connectee-demo',
        storageBucket: 'canne-connectee-demo.appspot.com',
      );

      await Firebase.initializeApp(
        options: firebaseOptions,
      );

      _isInitialized = true;

      if (kDebugMode) {
        print('FirebaseService: ✅ Initialisé avec succès');
      }

    } catch (e) {
      if (kDebugMode) {
        print('FirebaseService: ❌ Erreur d\'initialisation: $e');
      }
      
      // Ne pas bloquer l'application si Firebase échoue
      // L'authentification sociale sera simplement désactivée
      _isInitialized = false;
    }
  }

  /// Vérifie si Firebase est disponible et configuré
  bool isAvailable() {
    return _isInitialized;
  }

  /// Obtient les informations de configuration Firebase
  Map<String, dynamic> getConfigInfo() {
    if (!_isInitialized) {
      return {
        'status': 'not_initialized',
        'message': 'Firebase n\'est pas initialisé',
      };
    }

    try {
      final app = Firebase.app();
      return {
        'status': 'initialized',
        'app_name': app.name,
        'project_id': app.options.projectId,
        'app_id': app.options.appId,
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'Erreur lors de la récupération des informations: $e',
      };
    }
  }
}
