const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Alerte = require('../models/Alerte');
const authMiddleware = require('../middleware/auth');

const router = express.Router();

/**
 * @route GET /api/alertes
 * @desc Récupère toutes les alertes (compatible avec l'app mobile)
 */
router.get('/', [
  query('user_id').optional().isInt().withMessage('ID utilisateur invalide'),
  query('status').optional().isIn(['active', 'resolved', 'cancelled']).withMessage('Statut invalide'),
  query('type').optional().isIn(['sos', 'chute', 'panique', 'medical', 'autre']).withMessage('Type invalide'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite invalide')
], async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Paramètres invalides',
        details: errors.array()
      });
    }

    const filters = {
      user_id: req.query.user_id,
      status_alerte: req.query.status,
      type_alerte: req.query.type,
      limit: req.query.limit || 50
    };

    // Supprimer les filtres vides
    Object.keys(filters).forEach(key => {
      if (filters[key] === undefined || filters[key] === '') {
        delete filters[key];
      }
    });

    const alertes = await Alerte.findAll(filters);

    res.json({
      success: true,
      count: alertes.length,
      data: alertes
    });

  } catch (error) {
    console.error('Erreur récupération alertes:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des alertes',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route POST /api/alertes
 * @desc Crée une nouvelle alerte SOS (compatible avec l'app mobile)
 */
router.post('/', [
  body('user_id')
    .optional()
    .isInt()
    .withMessage('ID utilisateur invalide'),
  
  body('type_alerte')
    .isIn(['sos', 'chute', 'panique', 'medical', 'autre'])
    .withMessage('Type d\'alerte invalide'),
  
  body('message_alerte')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Message trop long (max 1000 caractères)'),
  
  body('niveau_urgence')
    .optional()
    .isIn(['faible', 'moyen', 'eleve', 'critique'])
    .withMessage('Niveau d\'urgence invalide'),
  
  body('latitude')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude invalide'),
  
  body('longitude')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude invalide'),
  
  body('adresse_approximative')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Adresse trop longue')
], async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const alerteData = {
      user_id: req.body.user_id || 1, // Utilisateur par défaut si non spécifié
      type_alerte: req.body.type_alerte,
      message_alerte: req.body.message_alerte,
      niveau_urgence: req.body.niveau_urgence || 'moyen',
      latitude: req.body.latitude,
      longitude: req.body.longitude,
      adresse_approximative: req.body.adresse_approximative
    };

    const nouvelleAlerte = await Alerte.create(alerteData);

    // Log pour le monitoring
    console.log(`🚨 NOUVELLE ALERTE SOS créée:`, {
      id: nouvelleAlerte.id,
      type: nouvelleAlerte.type_alerte,
      urgence: nouvelleAlerte.niveau_urgence,
      user_id: nouvelleAlerte.user_id,
      location: nouvelleAlerte.hasLocation ? 
        `${nouvelleAlerte.latitude}, ${nouvelleAlerte.longitude}` : 
        'Non spécifiée'
    });

    res.status(201).json({
      success: true,
      message: 'Alerte créée avec succès',
      data: nouvelleAlerte
    });

  } catch (error) {
    console.error('Erreur création alerte:', error);
    res.status(500).json({
      error: 'Erreur lors de la création de l\'alerte',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erreur interne'
    });
  }
});

/**
 * @route GET /api/alertes/:id
 * @desc Récupère une alerte spécifique
 */
router.get('/:id', async (req, res) => {
  try {
    const alerteId = parseInt(req.params.id);
    
    if (isNaN(alerteId)) {
      return res.status(400).json({
        error: 'ID d\'alerte invalide'
      });
    }

    const alerte = await Alerte.findById(alerteId);
    
    if (!alerte) {
      return res.status(404).json({
        error: 'Alerte non trouvée'
      });
    }

    res.json({
      success: true,
      data: alerte
    });

  } catch (error) {
    console.error('Erreur récupération alerte:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération de l\'alerte'
    });
  }
});

/**
 * @route PUT /api/alertes/:id/status
 * @desc Met à jour le statut d'une alerte
 */
router.put('/:id/status', [
  body('status_alerte')
    .isIn(['active', 'resolved', 'cancelled'])
    .withMessage('Statut invalide'),
  
  body('resolved_by')
    .optional()
    .isInt()
    .withMessage('ID du résolveur invalide')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array()
      });
    }

    const alerteId = parseInt(req.params.id);
    const { status_alerte, resolved_by } = req.body;

    if (isNaN(alerteId)) {
      return res.status(400).json({
        error: 'ID d\'alerte invalide'
      });
    }

    const alerte = await Alerte.findById(alerteId);
    if (!alerte) {
      return res.status(404).json({
        error: 'Alerte non trouvée'
      });
    }

    const alerteUpdated = await alerte.updateStatus(status_alerte, resolved_by);

    console.log(`📝 Alerte ${alerteId} mise à jour: ${status_alerte}`);

    res.json({
      success: true,
      message: 'Statut de l\'alerte mis à jour',
      data: alerteUpdated
    });

  } catch (error) {
    console.error('Erreur mise à jour alerte:', error);
    res.status(500).json({
      error: 'Erreur lors de la mise à jour de l\'alerte'
    });
  }
});

/**
 * @route DELETE /api/alertes/:id
 * @desc Supprime une alerte (compatible avec l'app mobile)
 */
router.delete('/:id', async (req, res) => {
  try {
    const alerteId = parseInt(req.params.id);
    
    if (isNaN(alerteId)) {
      return res.status(400).json({
        error: 'ID d\'alerte invalide'
      });
    }

    const alerte = await Alerte.findById(alerteId);
    if (!alerte) {
      return res.status(404).json({
        error: 'Alerte non trouvée'
      });
    }

    // Marquer comme annulée plutôt que supprimer
    await alerte.updateStatus('cancelled');

    console.log(`🗑️ Alerte ${alerteId} supprimée (marquée comme annulée)`);

    res.json({
      success: true,
      message: 'Alerte supprimée avec succès'
    });

  } catch (error) {
    console.error('Erreur suppression alerte:', error);
    res.status(500).json({
      error: 'Erreur lors de la suppression de l\'alerte'
    });
  }
});

/**
 * @route GET /api/alertes/stats/summary
 * @desc Statistiques des alertes
 */
router.get('/stats/summary', [
  query('user_id').optional().isInt().withMessage('ID utilisateur invalide')
], async (req, res) => {
  try {
    const userId = req.query.user_id ? parseInt(req.query.user_id) : null;
    const stats = await Alerte.getStatistics(userId);

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Erreur statistiques alertes:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des statistiques'
    });
  }
});

module.exports = router;
