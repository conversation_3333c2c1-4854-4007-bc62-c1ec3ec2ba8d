// Exemple de configuration backend Node.js pour l'authentification sociale
// Ce fichier montre comment implémenter l'authentification sociale côté serveur

const express = require('express');
const jwt = require('jsonwebtoken');
const { OAuth2Client } = require('google-auth-library');
const mysql = require('mysql2/promise');

const app = express();
app.use(express.json());

// Configuration
const JWT_SECRET = 'votre_secret_jwt_ici';
const GOOGLE_CLIENT_ID = 'votre_google_client_id_ici';

// Client Google OAuth
const googleClient = new OAuth2Client(GOOGLE_CLIENT_ID);

// Configuration base de données
const dbConfig = {
  host: 'localhost',
  user: 'votre_utilisateur',
  password: 'votre_mot_de_passe',
  database: 'canne_connectee'
};

// Route d'authentification sociale
app.post('/api/auth/social-login', async (req, res) => {
  try {
    const {
      provider,
      provider_id,
      email,
      display_name,
      photo_url,
      user_type,
      access_token,
      id_token
    } = req.body;

    console.log(`Tentative de connexion sociale: ${provider} pour ${email}`);

    // Vérification du token selon le fournisseur
    let verifiedUser = null;

    if (provider === 'google') {
      verifiedUser = await verifyGoogleToken(id_token);
    } else if (provider === 'apple') {
      verifiedUser = await verifyAppleToken(id_token);
    } else {
      return res.status(400).json({
        success: false,
        message: 'Fournisseur d\'authentification non supporté'
      });
    }

    if (!verifiedUser) {
      return res.status(401).json({
        success: false,
        message: 'Token d\'authentification invalide'
      });
    }

    // Connexion à la base de données
    const connection = await mysql.createConnection(dbConfig);

    try {
      // Vérifier si l'utilisateur existe déjà
      const [existingUsers] = await connection.execute(
        'SELECT * FROM utilisateurs WHERE email_utilisateur = ? OR social_provider_id = ?',
        [email, provider_id]
      );

      let user;

      if (existingUsers.length > 0) {
        // Utilisateur existant - mise à jour des informations sociales
        user = existingUsers[0];
        
        await connection.execute(
          `UPDATE utilisateurs SET 
           social_provider = ?, 
           social_provider_id = ?, 
           photo_url = ?, 
           derniere_connexion = NOW() 
           WHERE id_utilisateur = ?`,
          [provider, provider_id, photo_url, user.id_utilisateur]
        );

        console.log(`Utilisateur existant connecté: ${user.email_utilisateur}`);
      } else {
        // Nouvel utilisateur - création du compte
        const [result] = await connection.execute(
          `INSERT INTO utilisateurs (
            nom_utilisateur, 
            prenom_utilisateur, 
            email_utilisateur, 
            user_type, 
            social_provider, 
            social_provider_id, 
            photo_url, 
            date_creation, 
            derniere_connexion
          ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
          [
            extractLastName(display_name),
            extractFirstName(display_name),
            email,
            user_type,
            provider,
            provider_id,
            photo_url
          ]
        );

        // Récupérer l'utilisateur créé
        const [newUsers] = await connection.execute(
          'SELECT * FROM utilisateurs WHERE id_utilisateur = ?',
          [result.insertId]
        );

        user = newUsers[0];
        console.log(`Nouvel utilisateur créé: ${user.email_utilisateur}`);
      }

      // Générer le token JWT
      const token = jwt.sign(
        {
          userId: user.id_utilisateur,
          email: user.email_utilisateur,
          userType: user.user_type
        },
        JWT_SECRET,
        { expiresIn: '7d' }
      );

      // Réponse de succès
      res.json({
        success: true,
        message: 'Connexion réussie',
        token: token,
        user: {
          id_utilisateur: user.id_utilisateur,
          nom_utilisateur: user.nom_utilisateur,
          prenom_utilisateur: user.prenom_utilisateur,
          email_utilisateur: user.email_utilisateur,
          user_type: user.user_type,
          photo_url: user.photo_url,
          social_provider: user.social_provider
        }
      });

    } finally {
      await connection.end();
    }

  } catch (error) {
    console.error('Erreur authentification sociale:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur interne du serveur'
    });
  }
});

// Fonction de vérification du token Google
async function verifyGoogleToken(idToken) {
  try {
    const ticket = await googleClient.verifyIdToken({
      idToken: idToken,
      audience: GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();
    return {
      provider_id: payload.sub,
      email: payload.email,
      name: payload.name,
      picture: payload.picture,
      email_verified: payload.email_verified
    };
  } catch (error) {
    console.error('Erreur vérification token Google:', error);
    return null;
  }
}

// Fonction de vérification du token Apple (simplifiée)
async function verifyAppleToken(idToken) {
  try {
    // Note: La vérification des tokens Apple est plus complexe
    // Vous devrez implémenter la vérification avec les clés publiques d'Apple
    // Ceci est un exemple simplifié
    
    // Décoder le token (sans vérification pour l'exemple)
    const decoded = jwt.decode(idToken);
    
    if (decoded && decoded.email) {
      return {
        provider_id: decoded.sub,
        email: decoded.email,
        name: decoded.name || '',
        email_verified: decoded.email_verified
      };
    }
    
    return null;
  } catch (error) {
    console.error('Erreur vérification token Apple:', error);
    return null;
  }
}

// Fonctions utilitaires
function extractFirstName(displayName) {
  if (!displayName) return '';
  return displayName.split(' ')[0] || '';
}

function extractLastName(displayName) {
  if (!displayName) return '';
  const parts = displayName.split(' ');
  return parts.length > 1 ? parts.slice(1).join(' ') : '';
}

// Script SQL pour ajouter les colonnes nécessaires à la table utilisateurs
const sqlMigration = `
-- Ajouter les colonnes pour l'authentification sociale
ALTER TABLE utilisateurs 
ADD COLUMN social_provider VARCHAR(50) NULL,
ADD COLUMN social_provider_id VARCHAR(255) NULL,
ADD COLUMN photo_url TEXT NULL,
ADD COLUMN derniere_connexion TIMESTAMP NULL;

-- Index pour améliorer les performances
CREATE INDEX idx_social_provider ON utilisateurs(social_provider, social_provider_id);
CREATE INDEX idx_email ON utilisateurs(email_utilisateur);
`;

console.log('Configuration backend pour authentification sociale');
console.log('N\'oubliez pas d\'exécuter le script SQL suivant:');
console.log(sqlMigration);

module.exports = app;
