const jwt = require('jsonwebtoken');
const User = require('../models/User');

/**
 * Middleware d'authentification JWT
 * Vérifie le token et ajoute l'utilisateur à req.user
 */
const authMiddleware = async (req, res, next) => {
  try {
    // Récupérer le token depuis l'en-tête Authorization
    const authHeader = req.header('Authorization');
    
    if (!authHeader) {
      return res.status(401).json({
        error: 'Accès refusé. Token manquant.'
      });
    }

    // Vérifier le format "Bearer token"
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.slice(7) 
      : authHeader;

    if (!token) {
      return res.status(401).json({
        error: 'Accès refusé. Format de token invalide.'
      });
    }

    // Vérifier et décoder le token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Récupérer l'utilisateur depuis la base de données
    const user = await User.findById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({
        error: 'Token invalide. Utilisateur non trouvé.'
      });
    }

    if (!user.isActive) {
      return res.status(403).json({
        error: 'Compte désactivé. Contactez l\'administrateur.'
      });
    }

    // Ajouter l'utilisateur à la requête
    req.user = user;
    req.userId = user.id;
    
    next();

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Token invalide.'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Token expiré. Veuillez vous reconnecter.'
      });
    }

    console.error('Erreur middleware auth:', error);
    res.status(500).json({
      error: 'Erreur d\'authentification.'
    });
  }
};

/**
 * Middleware d'authentification optionnel
 * N'interrompt pas la requête si pas de token, mais ajoute l'utilisateur si token valide
 */
const optionalAuthMiddleware = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    
    if (!authHeader) {
      return next();
    }

    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.slice(7) 
      : authHeader;

    if (!token) {
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId);
    
    if (user && user.isActive) {
      req.user = user;
      req.userId = user.id;
    }
    
    next();

  } catch (error) {
    // En cas d'erreur, continuer sans utilisateur
    next();
  }
};

/**
 * Middleware pour vérifier les rôles (si implémenté plus tard)
 */
const roleMiddleware = (roles = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentification requise.'
      });
    }

    // Pour l'instant, tous les utilisateurs ont les mêmes droits
    // Cette fonction peut être étendue pour gérer les rôles
    next();
  };
};

/**
 * Middleware pour vérifier que l'utilisateur accède à ses propres données
 */
const ownershipMiddleware = (userIdParam = 'userId') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentification requise.'
      });
    }

    const requestedUserId = parseInt(req.params[userIdParam]) || parseInt(req.body.user_id);
    
    if (requestedUserId && requestedUserId !== req.user.id) {
      return res.status(403).json({
        error: 'Accès refusé. Vous ne pouvez accéder qu\'à vos propres données.'
      });
    }

    next();
  };
};

module.exports = {
  authMiddleware,
  optionalAuthMiddleware,
  roleMiddleware,
  ownershipMiddleware
};
